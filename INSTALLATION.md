# Lead Generation Django - Server Installation Guide

This document provides step-by-step instructions for installing and deploying the Lead Generation Django application on a server.

## Table of Contents
- [System Requirements](#system-requirements)
- [Prerequisites](#prerequisites)
- [Installation Steps](#installation-steps)
- [Configuration](#configuration)
- [Running the Application](#running-the-application)
- [Production Deployment](#production-deployment)
- [Troubleshooting](#troubleshooting)

## System Requirements

### Minimum Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+ / Debian 11+
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 10GB minimum, 20GB recommended
- **CPU**: 2 cores minimum, 4 cores recommended
- **Network**: Stable internet connection for web scraping

### Supported Python Versions
- Python 3.8+
- Python 3.11.4 (recommended and tested)

## Prerequisites

### 1. Update System Packages
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
# or for newer versions
sudo dnf update -y
```

### 2. Install Python 3.11+
```bash
# Ubuntu/Debian
sudo apt install python3.11 python3.11-venv python3.11-dev python3-pip -y

# CentOS/RHEL (using EPEL)
sudo dnf install epel-release -y
sudo dnf install python3.11 python3.11-pip python3.11-devel -y
```

### 3. Install System Dependencies
```bash
# Ubuntu/Debian
sudo apt install -y \
    git \
    curl \
    wget \
    build-essential \
    libssl-dev \
    libffi-dev \
    libxml2-dev \
    libxslt1-dev \
    libjpeg-dev \
    libpng-dev \
    zlib1g-dev \
    postgresql-client \
    libpq-dev

# CentOS/RHEL
sudo dnf groupinstall "Development Tools" -y
sudo dnf install -y \
    git \
    curl \
    wget \
    openssl-devel \
    libffi-devel \
    libxml2-devel \
    libxslt-devel \
    libjpeg-devel \
    libpng-devel \
    zlib-devel \
    postgresql-devel
```

### 4. Install Playwright Dependencies
```bash
# Install required libraries for Playwright browsers
# Ubuntu/Debian
sudo apt install -y \
    libnss3 \
    libnspr4 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libgtk-3-0 \
    libatspi2.0-0 \
    libxrandr2 \
    libasound2 \
    libxss1 \
    libgl1-mesa-glx

# CentOS/RHEL
sudo dnf install -y \
    nss \
    nspr \
    at-spi2-atk \
    libdrm \
    libxkbcommon \
    gtk3 \
    at-spi2-core \
    libXrandr \
    alsa-lib \
    libXScrnSaver \
    mesa-libGL
```

## Installation Steps

### 1. Clone the Repository
```bash
# Create application directory
sudo mkdir -p /opt/leadgen-django
sudo chown $USER:$USER /opt/leadgen-django
cd /opt/leadgen-django

# Clone the repository
git clone <repository-url> .
# or if you have the files locally, copy them to /opt/leadgen-django
```

### 2. Create Virtual Environment
```bash
# Create virtual environment with .venv naming convention
python3.11 -m venv .venv

# Activate virtual environment
source .venv/bin/activate

# Verify Python version
python --version  # Should show Python 3.11.x
```

### 3. Install Python Dependencies
```bash
# Ensure virtual environment is activated
source .venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install project dependencies
pip install -r map_serp_scraper/requirements.txt

# Install additional required packages
pip install ultimate-sitemap-parser
```

### 4. Install Playwright Browsers
```bash
# Activate virtual environment
source .venv/bin/activate

# Install Playwright browsers (this may take several minutes)
playwright install

# Install system dependencies for Playwright
playwright install-deps
```

## Configuration

### 1. Environment Variables
Create a `.env` file in the project root:
```bash
# Copy example environment file
cp .env.example .env  # if exists, or create manually

# Edit environment variables
nano .env
```

Example `.env` file:
```env
# Django Settings
DEBUG=False
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=your-domain.com,your-server-ip

# Database Configuration (if using PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/leadgen_db

# Scraping Configuration
USE_PROXY=True
SCRAPING_DELAY=1.0

# OpenAI API (if using AI features)
OPENAI_API_KEY=your-openai-api-key-here
```

### 2. Database Setup (Optional - if using PostgreSQL)
```bash
# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y  # Ubuntu/Debian
# or
sudo dnf install postgresql postgresql-server postgresql-contrib -y  # CentOS/RHEL

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE leadgen_db;
CREATE USER leadgen_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE leadgen_db TO leadgen_user;
\q
```

### 3. Django Configuration
```bash
# Activate virtual environment
source .venv/bin/activate

# Navigate to Django project
cd map_serp_scraper

# Run migrations (if using database)
python manage.py migrate

# Create superuser (optional)
python manage.py createsuperuser

# Collect static files (for production)
python manage.py collectstatic --noinput
```

## Running the Application

### 1. Development Mode
```bash
# Activate virtual environment
source .venv/bin/activate

# Navigate to project directory
cd map_serp_scraper

# Run development server
python manage.py runserver 0.0.0.0:8000
```

### 2. Test the Installation
```bash
# Test site scraper
python test_site_scraper.py

# Test sitemap tool
python test_sitemap_tool.py
```

## Production Deployment

### 1. Install Production Server (Gunicorn)
```bash
# Activate virtual environment
source .venv/bin/activate

# Install Gunicorn
pip install gunicorn
```

### 2. Create Gunicorn Configuration
Create `/opt/leadgen-django/gunicorn.conf.py`:
```python
bind = "0.0.0.0:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 300
keepalive = 2
preload_app = True
```

### 3. Create Systemd Service
Create `/etc/systemd/system/leadgen-django.service`:
```ini
[Unit]
Description=Lead Generation Django Application
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/opt/leadgen-django/map_serp_scraper
Environment=PATH=/opt/leadgen-django/.venv/bin
ExecStart=/opt/leadgen-django/.venv/bin/gunicorn --config /opt/leadgen-django/gunicorn.conf.py map_serp_scraper.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 4. Start and Enable Service
```bash
# Set proper permissions
sudo chown -R www-data:www-data /opt/leadgen-django

# Reload systemd and start service
sudo systemctl daemon-reload
sudo systemctl start leadgen-django
sudo systemctl enable leadgen-django

# Check status
sudo systemctl status leadgen-django
```

### 5. Configure Nginx (Optional)
Install and configure Nginx as reverse proxy:
```bash
# Install Nginx
sudo apt install nginx -y  # Ubuntu/Debian
# or
sudo dnf install nginx -y  # CentOS/RHEL

# Create Nginx configuration
sudo nano /etc/nginx/sites-available/leadgen-django
```

Example Nginx configuration:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    location /static/ {
        alias /opt/leadgen-django/map_serp_scraper/staticfiles/;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/leadgen-django /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Troubleshooting

### Common Issues

1. **Playwright Browser Installation Fails**
   ```bash
   # Try installing with sudo
   sudo .venv/bin/playwright install
   sudo .venv/bin/playwright install-deps
   ```

2. **Permission Denied Errors**
   ```bash
   # Fix ownership
   sudo chown -R $USER:$USER /opt/leadgen-django
   ```

3. **Memory Issues During Scraping**
   - Increase server RAM
   - Reduce concurrent scraping operations
   - Add swap space

4. **Network Timeouts**
   - Check firewall settings
   - Increase timeout values in configuration
   - Verify internet connectivity

### Log Files
- Application logs: `/opt/leadgen-django/logs/`
- System service logs: `sudo journalctl -u leadgen-django -f`
- Nginx logs: `/var/log/nginx/`

### Performance Optimization
- Use Redis for caching
- Configure database connection pooling
- Implement rate limiting for scraping
- Use CDN for static files

## Security Considerations

1. **Firewall Configuration**
   ```bash
   sudo ufw allow ssh
   sudo ufw allow 80
   sudo ufw allow 443
   sudo ufw enable
   ```

2. **SSL Certificate (Let's Encrypt)**
   ```bash
   sudo apt install certbot python3-certbot-nginx -y
   sudo certbot --nginx -d your-domain.com
   ```

3. **Regular Updates**
   ```bash
   # Update system packages regularly
   sudo apt update && sudo apt upgrade -y
   
   # Update Python packages
   source .venv/bin/activate
   pip list --outdated
   ```

## Support

For issues and support:
- Check logs for error messages
- Verify all dependencies are installed
- Ensure proper file permissions
- Test network connectivity
- Review configuration files

---

**Note**: This installation guide assumes a clean server environment. Adjust commands and paths according to your specific setup and requirements.
