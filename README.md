# Lead Generation Django Application

A powerful Django-based web scraping application designed for lead generation through automated contact information extraction from websites.

## 🚀 Features

- **Website Contact Scraping**: Automatically extract emails and phone numbers from websites
- **Sitemap Discovery**: Parse website sitemaps to discover all available pages
- **AI-Powered Extraction**: Uses advanced AI models for intelligent content extraction
- **Django Web Framework**: Built on robust Django framework for scalability
- **Playwright Integration**: Modern browser automation for JavaScript-heavy sites
- **Production Ready**: Includes comprehensive deployment documentation

## 📋 Quick Start

### Prerequisites

- Python 3.8+ (Python 3.11.4 recommended)
- Git
- 4GB+ RAM recommended

### Local Development Setup

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/leadgen-django.git
   cd leadgen-django
   ```

2. **Create virtual environment**

   ```bash
   python3 -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**

   ```bash
   pip install -r map_serp_scraper/requirements.txt
   pip install ultimate-sitemap-parser
   ```

4. **Install Playwright browsers**

   ```bash
   playwright install
   ```

5. **Run the application**
   ```bash
   cd map_serp_scraper
   python manage.py runserver
   ```

## 🧪 Testing

Run the included test scripts to verify functionality:

```bash
cd map_serp_scraper

# Test site scraper
python test_site_scraper.py

# Test sitemap tool
python test_sitemap_tool.py
```

## 📁 Project Structure

```
leadgen-django/
├── map_serp_scraper/           # Main Django application
│   ├── manage.py               # Django management script
│   ├── requirements.txt        # Python dependencies
│   ├── map_serp_scraper/       # Django project settings
│   ├── scraper/                # Main scraper application
│   │   ├── site_scraper_service.py    # Website contact scraping
│   │   ├── sitemap_tool.py             # Sitemap parsing
│   │   ├── models.py                   # Database models
│   │   └── views.py                    # Web views
│   ├── test_site_scraper.py    # Site scraper tests
│   └── test_sitemap_tool.py    # Sitemap tool tests
├── INSTALLATION.md             # Detailed installation guide
├── README.md                   # This file
└── .gitignore                  # Git ignore rules
```

## 🔧 Core Components

### Site Scraper Service

- Extracts contact information (emails, phone numbers) from websites
- Uses AI-powered content analysis
- Supports deep crawling with sitemap discovery
- Handles JavaScript-rendered content

### Sitemap Tool

- Discovers and parses website sitemaps
- Returns comprehensive URL lists for crawling
- Handles various sitemap formats

### Django Framework

- Web interface for scraping operations
- Database models for storing results
- RESTful API endpoints
- Admin interface for management

## 🚀 Production Deployment

For production deployment on servers, see the comprehensive [INSTALLATION.md](INSTALLATION.md) guide which includes:

- System requirements and prerequisites
- Step-by-step server setup
- Database configuration
- Gunicorn and Nginx setup
- SSL certificate configuration
- Security best practices
- Troubleshooting guide

## 📊 Dependencies

### Core Dependencies

- **Django 4.2.23**: Web framework
- **Crawl4AI 0.6.3**: AI-powered web crawling
- **Playwright 1.52.0**: Browser automation
- **BeautifulSoup4**: HTML parsing
- **LangChain**: AI/LLM integration
- **OpenAI**: AI model access

### Full dependency list available in `map_serp_scraper/requirements.txt`

## 🔒 Environment Variables

Create a `.env` file for configuration:

```env
# Django Settings
DEBUG=False
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=your-domain.com

# Database (optional)
DATABASE_URL=postgresql://user:pass@localhost/db

# AI/Scraping
OPENAI_API_KEY=your-openai-key
USE_PROXY=True
```

## 📝 Usage Examples

### Basic Site Scraping

```python
from scraper.site_scraper_service import scrape_site_contact_info

# Scrape contact information from a website
result = scrape_site_contact_info("https://example.com")
print(result)
```

### Sitemap Discovery

```python
from scraper.sitemap_tool import get_sitemap_urls

# Get all URLs from a website's sitemap
urls = get_sitemap_urls("https://example.com")
print(f"Found {len(urls)} URLs")
```

## 🐛 Troubleshooting

### Common Issues

1. **Playwright Installation Issues**

   ```bash
   playwright install-deps
   sudo playwright install
   ```

2. **Memory Issues**

   - Increase server RAM
   - Reduce concurrent operations
   - Add swap space

3. **Permission Errors**
   ```bash
   sudo chown -R $USER:$USER /path/to/project
   ```

### Logs and Debugging

- Check Django logs: `python manage.py runserver --verbosity=2`
- Playwright logs: Set `DEBUG=True` in Django settings
- System logs: `journalctl -u your-service-name`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Add tests for new functionality
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Check the [INSTALLATION.md](INSTALLATION.md) for detailed setup instructions
- Review the troubleshooting section above
- Create an issue in the GitHub repository
- Check logs for error messages

## 🔄 Version History

- **v1.0.0**: Initial release with core scraping functionality
- Main branch contains the latest stable version

---

**Note**: This application is designed for legitimate lead generation purposes. Please ensure compliance with website terms of service and applicable laws when scraping websites.
