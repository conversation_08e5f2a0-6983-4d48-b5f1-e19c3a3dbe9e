[{"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "get_asgi_application", "importPath": "django.core.asgi", "description": "django.core.asgi", "isExtraImport": true, "detail": "django.core.asgi", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "admin", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "admin", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "include", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "path", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "path", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "get_wsgi_application", "importPath": "django.core.wsgi", "description": "django.core.wsgi", "isExtraImport": true, "detail": "django.core.wsgi", "documentation": {}}, {"label": "migrations", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "migrations", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "migrations", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "AppConfig", "importPath": "django.apps", "description": "django.apps", "isExtraImport": true, "detail": "django.apps", "documentation": {}}, {"label": "psycopg2", "kind": 6, "isExtraImport": true, "importPath": "psycopg2", "description": "psycopg2", "detail": "psycopg2", "documentation": {}}, {"label": "sql", "importPath": "psycopg2", "description": "psycopg2", "isExtraImport": true, "detail": "psycopg2", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "ChatOpenAI", "importPath": "langchain_openai", "description": "langchain_openai", "isExtraImport": true, "detail": "langchain_openai", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "AsyncWebCrawler", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "BrowserConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "CrawlerRunConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "CacheMode", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "ProxyConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "LLMConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "LLMExtractionStrategy", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "AsyncWebCrawler", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "BrowserConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "CrawlerRunConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "CacheMode", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "KeywordRelevanceScorer", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "LLMConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "LLMExtractionStrategy", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "ProxyConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "AsyncWebCrawler", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "BrowserConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "ProxyConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "URLValidator", "importPath": "django.core.validators", "description": "django.core.validators", "isExtraImport": true, "detail": "django.core.validators", "documentation": {}}, {"label": "ValidationError", "importPath": "django.core.exceptions", "description": "django.core.exceptions", "isExtraImport": true, "detail": "django.core.exceptions", "documentation": {}}, {"label": "get_sublocations", "importPath": "scraper.langgraph_workflow", "description": "scraper.langgraph_workflow", "isExtraImport": true, "detail": "scraper.langgraph_workflow", "documentation": {}}, {"label": "scrape_google_maps", "importPath": "scraper.map_scraper_service", "description": "scraper.map_scraper_service", "isExtraImport": true, "detail": "scraper.map_scraper_service", "documentation": {}}, {"label": "scrape_google_maps", "importPath": "scraper.map_scraper_service", "description": "scraper.map_scraper_service", "isExtraImport": true, "detail": "scraper.map_scraper_service", "documentation": {}}, {"label": "insert_scrape_entry", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "insert_scrape_entry_django", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "insert_scrape_entry_django", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "bulk_insert_scrape_entries", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "insert_scrape_entry_django", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "bulk_insert_scrape_entries", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "insert_scrape_entry", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "insert_scrape_entry_django", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "bulk_insert_scrape_entries", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "website_exists", "importPath": "scraper.db_service", "description": "scraper.db_service", "isExtraImport": true, "detail": "scraper.db_service", "documentation": {}}, {"label": "uuid", "kind": 6, "isExtraImport": true, "importPath": "uuid", "description": "uuid", "detail": "uuid", "documentation": {}}, {"label": "re", "kind": 6, "isExtraImport": true, "importPath": "re", "description": "re", "detail": "re", "documentation": {}}, {"label": "urlparse", "importPath": "urllib.parse", "description": "urllib.parse", "isExtraImport": true, "detail": "urllib.parse", "documentation": {}}, {"label": "url<PERSON>", "importPath": "urllib.parse", "description": "urllib.parse", "isExtraImport": true, "detail": "urllib.parse", "documentation": {}}, {"label": "BFSDeepCrawlStrategy", "importPath": "crawl4ai.deep_crawling", "description": "crawl4ai.deep_crawling", "isExtraImport": true, "detail": "crawl4ai.deep_crawling", "documentation": {}}, {"label": "LXMLWebScrapingStrategy", "importPath": "crawl4ai.content_scraping_strategy", "description": "crawl4ai.content_scraping_strategy", "isExtraImport": true, "detail": "crawl4ai.content_scraping_strategy", "documentation": {}}, {"label": "ElementTree", "importPath": "xml.etree", "description": "xml.etree", "isExtraImport": true, "detail": "xml.etree", "documentation": {}}, {"label": "sitemap_tree_for_homepage", "importPath": "usp.tree", "description": "usp.tree", "isExtraImport": true, "detail": "usp.tree", "documentation": {}}, {"label": "TestCase", "importPath": "django.test", "description": "django.test", "isExtraImport": true, "detail": "django.test", "documentation": {}}, {"label": "map_search_view", "importPath": "scraper.views", "description": "scraper.views", "isExtraImport": true, "detail": "scraper.views", "documentation": {}}, {"label": "render", "importPath": "django.shortcuts", "description": "django.shortcuts", "isExtraImport": true, "detail": "django.shortcuts", "documentation": {}}, {"label": "JsonResponse", "importPath": "django.http", "description": "django.http", "isExtraImport": true, "detail": "django.http", "documentation": {}}, {"label": "csrf_exempt", "importPath": "django.views.decorators.csrf", "description": "django.views.decorators.csrf", "isExtraImport": true, "detail": "django.views.decorators.csrf", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "django", "kind": 6, "isExtraImport": true, "importPath": "django", "description": "django", "detail": "django", "documentation": {}}, {"label": "BusinessScrape", "importPath": "scraper.models", "description": "scraper.models", "isExtraImport": true, "detail": "scraper.models", "documentation": {}}, {"label": "BusinessScrape", "importPath": "scraper.models", "description": "scraper.models", "isExtraImport": true, "detail": "scraper.models", "documentation": {}}, {"label": "BusinessScrape", "importPath": "scraper.models", "description": "scraper.models", "isExtraImport": true, "detail": "scraper.models", "documentation": {}}, {"label": "BusinessScrape", "importPath": "scraper.models", "description": "scraper.models", "isExtraImport": true, "detail": "scraper.models", "documentation": {}}, {"label": "run_pipeline", "importPath": "scraper.run_pipeline", "description": "scraper.run_pipeline", "isExtraImport": true, "detail": "scraper.run_pipeline", "documentation": {}}, {"label": "scrape_site_contact_info", "importPath": "scraper.site_scraper_service", "description": "scraper.site_scraper_service", "isExtraImport": true, "detail": "scraper.site_scraper_service", "documentation": {}}, {"label": "scrape_sitemap", "importPath": "scraper.sitemap_scraper", "description": "scraper.sitemap_scraper", "isExtraImport": true, "detail": "scraper.sitemap_scraper", "documentation": {}}, {"label": "get_sitemap_urls", "importPath": "scraper.sitemap_tool", "description": "scraper.sitemap_tool", "isExtraImport": true, "detail": "scraper.sitemap_tool", "documentation": {}}, {"label": "application", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.asgi", "description": "map_serp_scraper.map_serp_scraper.asgi", "peekOfCode": "application = get_asgi_application()", "detail": "map_serp_scraper.map_serp_scraper.asgi", "documentation": {}}, {"label": "DJANGO_SECRET_KEY", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "DJANGO_SECRET_KEY = os.getenv(\"DJANGO_SECRET_KEY\")\n# Build paths inside the project like this: BASE_DIR / 'subdir'.\nBASE_DIR = Path(__file__).resolve().parent.parent\n# Quick-start development settings - unsuitable for production\n# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/\n# SECURITY WARNING: keep the secret key used in production secret!\nSECRET_KEY = DJANGO_SECRET_KEY\n# SECURITY WARNING: don't run with debug turned on in production!\nDEBUG = True\nALLOWED_HOSTS = []", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "BASE_DIR", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "BASE_DIR = Path(__file__).resolve().parent.parent\n# Quick-start development settings - unsuitable for production\n# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/\n# SECURITY WARNING: keep the secret key used in production secret!\nSECRET_KEY = DJANGO_SECRET_KEY\n# SECURITY WARNING: don't run with debug turned on in production!\nDEBUG = True\nALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "SECRET_KEY", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "SECRET_KEY = DJANGO_SECRET_KEY\n# SECURITY WARNING: don't run with debug turned on in production!\nDEBUG = True\nALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [\n    'django.contrib.admin',\n    'django.contrib.auth',\n    'django.contrib.contenttypes',\n    'django.contrib.sessions',", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "DEBUG", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "DEBUG = True\nALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [\n    'django.contrib.admin',\n    'django.contrib.auth',\n    'django.contrib.contenttypes',\n    'django.contrib.sessions',\n    'django.contrib.messages',\n    'django.contrib.staticfiles',", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "ALLOWED_HOSTS", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "ALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [\n    'django.contrib.admin',\n    'django.contrib.auth',\n    'django.contrib.contenttypes',\n    'django.contrib.sessions',\n    'django.contrib.messages',\n    'django.contrib.staticfiles',\n    'scraper',", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "INSTALLED_APPS", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "INSTALLED_APPS = [\n    'django.contrib.admin',\n    'django.contrib.auth',\n    'django.contrib.contenttypes',\n    'django.contrib.sessions',\n    'django.contrib.messages',\n    'django.contrib.staticfiles',\n    'scraper',\n]\nMIDDLEWARE = [", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "MIDDLEWARE", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "MIDDLEWARE = [\n    'django.middleware.security.SecurityMiddleware',\n    'django.contrib.sessions.middleware.SessionMiddleware',\n    'django.middleware.common.CommonMiddleware',\n    'django.middleware.csrf.CsrfViewMiddleware',\n    'django.contrib.auth.middleware.AuthenticationMiddleware',\n    'django.contrib.messages.middleware.MessageMiddleware',\n    'django.middleware.clickjacking.XFrameOptionsMiddleware',\n]\nROOT_URLCONF = 'map_serp_scraper.urls'", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "ROOT_URLCONF", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "ROOT_URLCONF = 'map_serp_scraper.urls'\nTEMPLATES = [\n    {\n        'BACKEND': 'django.template.backends.django.DjangoTemplates',\n        'DIRS': [],\n        'APP_DIRS': True,\n        'OPTIONS': {\n            'context_processors': [\n                'django.template.context_processors.debug',\n                'django.template.context_processors.request',", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "TEMPLATES", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "TEMPLATES = [\n    {\n        'BACKEND': 'django.template.backends.django.DjangoTemplates',\n        'DIRS': [],\n        'APP_DIRS': True,\n        'OPTIONS': {\n            'context_processors': [\n                'django.template.context_processors.debug',\n                'django.template.context_processors.request',\n                'django.contrib.auth.context_processors.auth',", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "WSGI_APPLICATION", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "WSGI_APPLICATION = 'map_serp_scraper.wsgi.application'\n# Database\n# https://docs.djangoproject.com/en/4.2/ref/settings/#databases\nDATABASES = {\n    'default': {\n        'ENGINE': 'django.db.backends.postgresql',\n        'NAME': os.getenv('DB_NAME'),\n        'USER': os.getenv('DB_USER'),\n        'PASSWORD': os.getenv('DB_PASS'),\n        'HOST': os.getenv('DB_HOST'),", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "DATABASES", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "DATABASES = {\n    'default': {\n        'ENGINE': 'django.db.backends.postgresql',\n        'NAME': os.getenv('DB_NAME'),\n        'USER': os.getenv('DB_USER'),\n        'PASSWORD': os.getenv('DB_PASS'),\n        'HOST': os.getenv('DB_HOST'),\n        'PORT': os.getenv('DB_PORT'),\n    }\n}", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "AUTH_PASSWORD_VALIDATORS", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "AUTH_PASSWORD_VALIDATORS = [\n    {\n        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',\n    },", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "LANGUAGE_CODE", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "LANGUAGE_CODE = 'en-us'\nTIME_ZONE = 'UTC'\nUSE_I18N = True\nUSE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/4.2/howto/static-files/\nSTATIC_URL = 'static/'\n# Default primary key field type\n# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "TIME_ZONE", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "TIME_ZONE = 'UTC'\nUSE_I18N = True\nUSE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/4.2/howto/static-files/\nSTATIC_URL = 'static/'\n# Default primary key field type\n# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'\nLOG_DIR = Path(BASE_DIR) / \"logs\"", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "USE_I18N", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "USE_I18N = True\nUSE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/4.2/howto/static-files/\nSTATIC_URL = 'static/'\n# Default primary key field type\n# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'\nLOG_DIR = Path(BASE_DIR) / \"logs\"\nLOG_DIR.mkdir(exist_ok=True)", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "USE_TZ", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "USE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/4.2/howto/static-files/\nSTATIC_URL = 'static/'\n# Default primary key field type\n# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'\nLOG_DIR = Path(BASE_DIR) / \"logs\"\nLOG_DIR.mkdir(exist_ok=True)\nLOGGING = {", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "STATIC_URL", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "STATIC_URL = 'static/'\n# Default primary key field type\n# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'\nLOG_DIR = Path(BASE_DIR) / \"logs\"\nLOG_DIR.mkdir(exist_ok=True)\nLOGGING = {\n    \"version\": 1,\n    \"disable_existing_loggers\": False,\n    \"formatters\": {", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "DEFAULT_AUTO_FIELD", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'\nLOG_DIR = Path(BASE_DIR) / \"logs\"\nLOG_DIR.mkdir(exist_ok=True)\nLOGGING = {\n    \"version\": 1,\n    \"disable_existing_loggers\": False,\n    \"formatters\": {\n        \"verbose\": {\n            \"format\": \"%(asctime)s [%(levelname)s] [%(name)s] %(message)s\"\n        },", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "LOG_DIR", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "LOG_DIR = Path(BASE_DIR) / \"logs\"\nLOG_DIR.mkdir(exist_ok=True)\nLOGGING = {\n    \"version\": 1,\n    \"disable_existing_loggers\": False,\n    \"formatters\": {\n        \"verbose\": {\n            \"format\": \"%(asctime)s [%(levelname)s] [%(name)s] %(message)s\"\n        },\n        \"simple\": {", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "LOGGING", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.settings", "description": "map_serp_scraper.map_serp_scraper.settings", "peekOfCode": "LOGGING = {\n    \"version\": 1,\n    \"disable_existing_loggers\": <PERSON>als<PERSON>,\n    \"formatters\": {\n        \"verbose\": {\n            \"format\": \"%(asctime)s [%(levelname)s] [%(name)s] %(message)s\"\n        },\n        \"simple\": {\n            \"format\": \"[%(levelname)s] %(message)s\"\n        },", "detail": "map_serp_scraper.map_serp_scraper.settings", "documentation": {}}, {"label": "url<PERSON><PERSON><PERSON>", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.urls", "description": "map_serp_scraper.map_serp_scraper.urls", "peekOfCode": "urlpatterns = [\n    path('admin/', admin.site.urls),\n    path('api/', include('scraper.urls')),  # 👈 include scraper URLs\n]", "detail": "map_serp_scraper.map_serp_scraper.urls", "documentation": {}}, {"label": "application", "kind": 5, "importPath": "map_serp_scraper.map_serp_scraper.wsgi", "description": "map_serp_scraper.map_serp_scraper.wsgi", "peekOfCode": "application = get_wsgi_application()", "detail": "map_serp_scraper.map_serp_scraper.wsgi", "documentation": {}}, {"label": "Migration", "kind": 6, "importPath": "map_serp_scraper.scraper.migrations.0001_initial", "description": "map_serp_scraper.scraper.migrations.0001_initial", "peekOfCode": "class Migration(migrations.Migration):\n    initial = True\n    dependencies = [\n    ]\n    operations = [\n        migrations.CreateModel(\n            name='BusinessScrape',\n            fields=[\n                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),\n                ('name', models.TextField(blank=True, help_text='Business name', null=True)),", "detail": "map_serp_scraper.scraper.migrations.0001_initial", "documentation": {}}, {"label": "Migration", "kind": 6, "importPath": "map_serp_scraper.scraper.migrations.0002_businessscrape_email_list", "description": "map_serp_scraper.scraper.migrations.0002_businessscrape_email_list", "peekOfCode": "class Migration(migrations.Migration):\n    dependencies = [\n        ('scraper', '0001_initial'),\n    ]\n    operations = [\n        migrations.AddField(\n            model_name='businessscrape',\n            name='email_list',\n            field=models.TextField(blank=True, help_text='List of email addresses separated by semicolons (;)', null=True),\n        ),", "detail": "map_serp_scraper.scraper.migrations.0002_businessscrape_email_list", "documentation": {}}, {"label": "Migration", "kind": 6, "importPath": "map_serp_scraper.scraper.migrations.0003_alter_businessscrape_phone", "description": "map_serp_scraper.scraper.migrations.0003_alter_businessscrape_phone", "peekOfCode": "class Migration(migrations.Migration):\n    dependencies = [\n        ('scraper', '0002_businessscrape_email_list'),\n    ]\n    operations = [\n        migrations.AlterField(\n            model_name='businessscrape',\n            name='phone',\n            field=models.CharField(blank=True, db_index=True, help_text='Business phone number (must be unique)', max_length=50, null=True, unique=True),\n        ),", "detail": "map_serp_scraper.scraper.migrations.0003_alter_businessscrape_phone", "documentation": {}}, {"label": "ScraperConfig", "kind": 6, "importPath": "map_serp_scraper.scraper.apps", "description": "map_serp_scraper.scraper.apps", "peekOfCode": "class ScraperConfig(AppConfig):\n    default_auto_field = 'django.db.models.BigAutoField'\n    name = 'scraper'", "detail": "map_serp_scraper.scraper.apps", "documentation": {}}, {"label": "website_exists", "kind": 2, "importPath": "map_serp_scraper.scraper.db_service", "description": "map_serp_scraper.scraper.db_service", "peekOfCode": "def website_exists(website_url: str) -> bool:\n    \"\"\"\n    Check if a website URL already exists in the database.\n    Args:\n        website_url (str): The website URL to check\n    Returns:\n        bool: True if website exists, False otherwise\n    \"\"\"\n    if not website_url:\n        return False", "detail": "map_serp_scraper.scraper.db_service", "documentation": {}}, {"label": "insert_scrape_entry", "kind": 2, "importPath": "map_serp_scraper.scraper.db_service", "description": "map_serp_scraper.scraper.db_service", "peekOfCode": "def insert_scrape_entry(entry: dict):\n    try:\n        conn = psycopg2.connect(\n            host=os.getenv(\"DB_HOST\"),\n            dbname=os.getenv(\"DB_NAME\"),\n            user=os.getenv(\"DB_USER\"),\n            password=os.getenv(\"DB_PASS\"),\n            port=os.getenv(\"DB_PORT\")\n        )\n        cur = conn.cursor()", "detail": "map_serp_scraper.scraper.db_service", "documentation": {}}, {"label": "insert_scrape_entry_django", "kind": 2, "importPath": "map_serp_scraper.scraper.db_service", "description": "map_serp_scraper.scraper.db_service", "peekOfCode": "def insert_scrape_entry_django(entry: dict):\n    \"\"\"\n    Django ORM-based function to insert scrape entry with unique website constraint.\n    Args:\n        entry (dict): Dictionary containing business information\n    Returns:\n        tuple: (success: bool, created: bool, message: str)\n    \"\"\"\n    try:\n        from .models import BusinessScrape", "detail": "map_serp_scraper.scraper.db_service", "documentation": {}}, {"label": "bulk_insert_scrape_entries", "kind": 2, "importPath": "map_serp_scraper.scraper.db_service", "description": "map_serp_scraper.scraper.db_service", "peekOfCode": "def bulk_insert_scrape_entries(entries: list):\n    \"\"\"\n    Bulk insert multiple scrape entries, skipping duplicates.\n    Args:\n        entries (list): List of dictionaries containing business information\n    Returns:\n        dict: Summary of insertion results\n    \"\"\"\n    results = {\n        \"total_entries\": len(entries),", "detail": "map_serp_scraper.scraper.db_service", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "map_serp_scraper.scraper.db_service", "description": "map_serp_scraper.scraper.db_service", "peekOfCode": "logger = logging.getLogger(\"pipeline\")\ndef website_exists(website_url: str) -> bool:\n    \"\"\"\n    Check if a website URL already exists in the database.\n    Args:\n        website_url (str): The website URL to check\n    Returns:\n        bool: True if website exists, False otherwise\n    \"\"\"\n    if not website_url:", "detail": "map_serp_scraper.scraper.db_service", "documentation": {}}, {"label": "test_db_connection", "kind": 2, "importPath": "map_serp_scraper.scraper.db_utils", "description": "map_serp_scraper.scraper.db_utils", "peekOfCode": "def test_db_connection() -> bool:\n    try:\n        conn = psycopg2.connect(\n            host=os.getenv(\"DB_HOST\"),\n            database=os.getenv(\"DB_NAME\"),\n            user=os.getenv(\"DB_USER\"),\n            password=os.getenv(\"DB_PASS\"),\n            port=os.getenv(\"DB_PORT\")\n        )\n        conn.close()", "detail": "map_serp_scraper.scraper.db_utils", "documentation": {}}, {"label": "SublocationsSchema", "kind": 6, "importPath": "map_serp_scraper.scraper.langgraph_workflow", "description": "map_serp_scraper.scraper.langgraph_workflow", "peekOfCode": "class SublocationsSchema(BaseModel):\n    sublocations: List[str] = Field(\n        ..., description=\"List down sublocations name of the given city\"\n    )\ndef get_sublocations(city: str) -> List[str]:\n    api_key = os.getenv(\"OPENAI_API_KEY\")\n    if not api_key:\n        raise RuntimeError(\"❌ OPENAI_API_KEY not found in environment\")\n    print(\"🔐 OPENAI KEY LOADED:\", api_key[:10])\n    llm = ChatOpenAI(", "detail": "map_serp_scraper.scraper.langgraph_workflow", "documentation": {}}, {"label": "get_sublocations", "kind": 2, "importPath": "map_serp_scraper.scraper.langgraph_workflow", "description": "map_serp_scraper.scraper.langgraph_workflow", "peekOfCode": "def get_sublocations(city: str) -> List[str]:\n    api_key = os.getenv(\"OPENAI_API_KEY\")\n    if not api_key:\n        raise RuntimeError(\"❌ OPENAI_API_KEY not found in environment\")\n    print(\"🔐 OPENAI KEY LOADED:\", api_key[:10])\n    llm = ChatOpenAI(\n        model=\"gpt-4o\", api_key=api_key\n    ).with_structured_output(SublocationsSchema)\n    prompt = f\"Give me a list of well-known sublocations (neighbourhoods) in {city}, India. Return only structured JSON.\"\n    result: SublocationsSchema = llm.invoke(prompt)", "detail": "map_serp_scraper.scraper.langgraph_workflow", "documentation": {}}, {"label": "MapBusinessEntry", "kind": 6, "importPath": "map_serp_scraper.scraper.map_scraper_service", "description": "map_serp_scraper.scraper.map_scraper_service", "peekOfCode": "class MapBusinessEntry(BaseModel):\n    name: str\n    rating: Optional[str]\n    address: Optional[str]\n    website: Optional[str]\n    phone: Optional[str]\n    category: Optional[str]\nasync def scrape_google_maps_async(query: str, use_proxy: bool = True) -> dict:\n    maps_query = query.replace(\" \", \"+\")\n    search_url = f\"https://www.google.com/maps/search/{maps_query}\"", "detail": "map_serp_scraper.scraper.map_scraper_service", "documentation": {}}, {"label": "scrape_google_maps", "kind": 2, "importPath": "map_serp_scraper.scraper.map_scraper_service", "description": "map_serp_scraper.scraper.map_scraper_service", "peekOfCode": "def scrape_google_maps(query: str, use_proxy: bool = True):\n    return asyncio.run(scrape_google_maps_async(query, use_proxy))", "detail": "map_serp_scraper.scraper.map_scraper_service", "documentation": {}}, {"label": "PROXY_USERNAME", "kind": 5, "importPath": "map_serp_scraper.scraper.map_scraper_service", "description": "map_serp_scraper.scraper.map_scraper_service", "peekOfCode": "PROXY_USERNAME = os.getenv(\"PROXY_USERNAME\")\nPROXY_PASSWORD = os.getenv(\"PROXY_PASSWORD\")\nclass MapBusinessEntry(BaseModel):\n    name: str\n    rating: Optional[str]\n    address: Optional[str]\n    website: Optional[str]\n    phone: Optional[str]\n    category: Optional[str]\nasync def scrape_google_maps_async(query: str, use_proxy: bool = True) -> dict:", "detail": "map_serp_scraper.scraper.map_scraper_service", "documentation": {}}, {"label": "PROXY_PASSWORD", "kind": 5, "importPath": "map_serp_scraper.scraper.map_scraper_service", "description": "map_serp_scraper.scraper.map_scraper_service", "peekOfCode": "PROXY_PASSWORD = os.getenv(\"PROXY_PASSWORD\")\nclass MapBusinessEntry(BaseModel):\n    name: str\n    rating: Optional[str]\n    address: Optional[str]\n    website: Optional[str]\n    phone: Optional[str]\n    category: Optional[str]\nasync def scrape_google_maps_async(query: str, use_proxy: bool = True) -> dict:\n    maps_query = query.replace(\" \", \"+\")", "detail": "map_serp_scraper.scraper.map_scraper_service", "documentation": {}}, {"label": "BusinessScrape", "kind": 6, "importPath": "map_serp_scraper.scraper.models", "description": "map_serp_scraper.scraper.models", "peekOfCode": "class BusinessScrape(models.Model):\n    \"\"\"\n    Model to store scraped business information with unique website constraint.\n    \"\"\"\n    name = models.TextField(blank=True, null=True, help_text=\"Business name\")\n    rating = models.CharField(\n        max_length=10, blank=True, null=True, help_text=\"Business rating\")\n    address = models.TextField(\n        blank=True, null=True, help_text=\"Business address\")\n    website = models.URLField(", "detail": "map_serp_scraper.scraper.models", "documentation": {}}, {"label": "run_pipeline", "kind": 2, "importPath": "map_serp_scraper.scraper.run_pipeline", "description": "map_serp_scraper.scraper.run_pipeline", "peekOfCode": "def run_pipeline(business_type: str, city: str) -> dict:\n    logging.info(\"🎯 Starting business data scraping pipeline...\")\n    logging.info(f\"🚀 Target: '{business_type}' listings in '{city}'\")\n    summary = {\"processed\": [], \"failed\": []}\n    try:\n        logging.info(\"📍 Fetching sublocations via LLM...\")\n        sublocations = get_sublocations(city)\n        if not sublocations:\n            raise RuntimeError(\"LLM returned no sublocations.\")\n        logging.info(", "detail": "map_serp_scraper.scraper.run_pipeline", "documentation": {}}, {"label": "ContactInfo", "kind": 6, "importPath": "map_serp_scraper.scraper.site_scraper_service", "description": "map_serp_scraper.scraper.site_scraper_service", "peekOfCode": "class ContactInfo(BaseModel):\n    url: str\n    emails: list[str]\n    phones: list[str]\nasync def scrape_site_contact_info_async(url: str, use_proxy: bool = True) -> dict:\n    parsed = urlparse(url)\n    base = f\"{parsed.scheme}://{parsed.netloc}\"\n    proxy_cfg = ProxyConfig(\n        server=\"https://core-residential.evomi-proxy.com:1001\",\n        username=PROXY_USERNAME, password=PROXY_PASSWORD", "detail": "map_serp_scraper.scraper.site_scraper_service", "documentation": {}}, {"label": "scrape_site_contact_info", "kind": 2, "importPath": "map_serp_scraper.scraper.site_scraper_service", "description": "map_serp_scraper.scraper.site_scraper_service", "peekOfCode": "def scrape_site_contact_info(url: str, use_proxy: bool = True):\n    return asyncio.run(scrape_site_contact_info_async(url, use_proxy))", "detail": "map_serp_scraper.scraper.site_scraper_service", "documentation": {}}, {"label": "PROXY_USERNAME", "kind": 5, "importPath": "map_serp_scraper.scraper.site_scraper_service", "description": "map_serp_scraper.scraper.site_scraper_service", "peekOfCode": "PROXY_USERNAME = os.getenv(\"PROXY_USERNAME\")\nPROXY_PASSWORD = os.getenv(\"PROXY_PASSWORD\")\nCONTACT_KEYWORDS = [\"contact\", \"about\", \"support\", \"help\", \"getintouch\"]\nEMAIL_REGEX = re.compile(r\"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+\")\nPHONE_REGEX = re.compile(r\"\\+?\\d[\\d\\-\\s()]{7,}\")\nclass ContactInfo(BaseModel):\n    url: str\n    emails: list[str]\n    phones: list[str]\nasync def scrape_site_contact_info_async(url: str, use_proxy: bool = True) -> dict:", "detail": "map_serp_scraper.scraper.site_scraper_service", "documentation": {}}, {"label": "PROXY_PASSWORD", "kind": 5, "importPath": "map_serp_scraper.scraper.site_scraper_service", "description": "map_serp_scraper.scraper.site_scraper_service", "peekOfCode": "PROXY_PASSWORD = os.getenv(\"PROXY_PASSWORD\")\nCONTACT_KEYWORDS = [\"contact\", \"about\", \"support\", \"help\", \"getintouch\"]\nEMAIL_REGEX = re.compile(r\"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+\")\nPHONE_REGEX = re.compile(r\"\\+?\\d[\\d\\-\\s()]{7,}\")\nclass ContactInfo(BaseModel):\n    url: str\n    emails: list[str]\n    phones: list[str]\nasync def scrape_site_contact_info_async(url: str, use_proxy: bool = True) -> dict:\n    parsed = urlparse(url)", "detail": "map_serp_scraper.scraper.site_scraper_service", "documentation": {}}, {"label": "CONTACT_KEYWORDS", "kind": 5, "importPath": "map_serp_scraper.scraper.site_scraper_service", "description": "map_serp_scraper.scraper.site_scraper_service", "peekOfCode": "CONTACT_KEYWORDS = [\"contact\", \"about\", \"support\", \"help\", \"getintouch\"]\nEMAIL_REGEX = re.compile(r\"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+\")\nPHONE_REGEX = re.compile(r\"\\+?\\d[\\d\\-\\s()]{7,}\")\nclass ContactInfo(BaseModel):\n    url: str\n    emails: list[str]\n    phones: list[str]\nasync def scrape_site_contact_info_async(url: str, use_proxy: bool = True) -> dict:\n    parsed = urlparse(url)\n    base = f\"{parsed.scheme}://{parsed.netloc}\"", "detail": "map_serp_scraper.scraper.site_scraper_service", "documentation": {}}, {"label": "EMAIL_REGEX", "kind": 5, "importPath": "map_serp_scraper.scraper.site_scraper_service", "description": "map_serp_scraper.scraper.site_scraper_service", "peekOfCode": "EMAIL_REGEX = re.compile(r\"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+\")\nPHONE_REGEX = re.compile(r\"\\+?\\d[\\d\\-\\s()]{7,}\")\nclass ContactInfo(BaseModel):\n    url: str\n    emails: list[str]\n    phones: list[str]\nasync def scrape_site_contact_info_async(url: str, use_proxy: bool = True) -> dict:\n    parsed = urlparse(url)\n    base = f\"{parsed.scheme}://{parsed.netloc}\"\n    proxy_cfg = ProxyConfig(", "detail": "map_serp_scraper.scraper.site_scraper_service", "documentation": {}}, {"label": "PHONE_REGEX", "kind": 5, "importPath": "map_serp_scraper.scraper.site_scraper_service", "description": "map_serp_scraper.scraper.site_scraper_service", "peekOfCode": "PHONE_REGEX = re.compile(r\"\\+?\\d[\\d\\-\\s()]{7,}\")\nclass ContactInfo(BaseModel):\n    url: str\n    emails: list[str]\n    phones: list[str]\nasync def scrape_site_contact_info_async(url: str, use_proxy: bool = True) -> dict:\n    parsed = urlparse(url)\n    base = f\"{parsed.scheme}://{parsed.netloc}\"\n    proxy_cfg = ProxyConfig(\n        server=\"https://core-residential.evomi-proxy.com:1001\",", "detail": "map_serp_scraper.scraper.site_scraper_service", "documentation": {}}, {"label": "extract_sitemap_urls_from_robots", "kind": 2, "importPath": "map_serp_scraper.scraper.sitemap_scraper", "description": "map_serp_scraper.scraper.sitemap_scraper", "peekOfCode": "def extract_sitemap_urls_from_robots(robots_text: str) -> list[str]:\n    sitemap_urls = re.findall(\n        r\"Sitemap:\\s*(https?://\\S+)\", robots_text, re.IGNORECASE)\n    return sitemap_urls\ndef parse_sitemap(xml_text: str) -> list[str]:\n    try:\n        root = ElementTree.fromstring(xml_text)\n        namespace = {\"ns\": \"http://www.sitemaps.org/schemas/sitemap/0.9\"}\n        urls = []\n        if root.tag.endswith(\"sitemapindex\"):", "detail": "map_serp_scraper.scraper.sitemap_scraper", "documentation": {}}, {"label": "parse_sitemap", "kind": 2, "importPath": "map_serp_scraper.scraper.sitemap_scraper", "description": "map_serp_scraper.scraper.sitemap_scraper", "peekOfCode": "def parse_sitemap(xml_text: str) -> list[str]:\n    try:\n        root = ElementTree.fromstring(xml_text)\n        namespace = {\"ns\": \"http://www.sitemaps.org/schemas/sitemap/0.9\"}\n        urls = []\n        if root.tag.endswith(\"sitemapindex\"):\n            sitemaps = root.findall(\"ns:sitemap/ns:loc\", namespace)\n            return [s.text.strip() for s in sitemaps if s is not None]\n        elif root.tag.endswith(\"urlset\"):\n            entries = root.findall(\"ns:url/ns:loc\", namespace)", "detail": "map_serp_scraper.scraper.sitemap_scraper", "documentation": {}}, {"label": "scrape_sitemap", "kind": 2, "importPath": "map_serp_scraper.scraper.sitemap_scraper", "description": "map_serp_scraper.scraper.sitemap_scraper", "peekOfCode": "def scrape_sitemap(base_url: str, use_proxy: bool = True):\n    return asyncio.run(scrape_sitemap_urls(base_url, use_proxy))", "detail": "map_serp_scraper.scraper.sitemap_scraper", "documentation": {}}, {"label": "PROXY_USERNAME", "kind": 5, "importPath": "map_serp_scraper.scraper.sitemap_scraper", "description": "map_serp_scraper.scraper.sitemap_scraper", "peekOfCode": "PROXY_USERNAME = os.getenv(\"PROXY_USERNAME\")\nPROXY_PASSWORD = os.getenv(\"PROXY_PASSWORD\")\nasync def fetch_url(crawler, url):\n    try:\n        response = await crawler.arun(url=url)\n        if response.success and response.html:\n            return response.html\n        else:\n            print(f\"⚠️ Failed to fetch: {url}\")\n            return None", "detail": "map_serp_scraper.scraper.sitemap_scraper", "documentation": {}}, {"label": "PROXY_PASSWORD", "kind": 5, "importPath": "map_serp_scraper.scraper.sitemap_scraper", "description": "map_serp_scraper.scraper.sitemap_scraper", "peekOfCode": "PROXY_PASSWORD = os.getenv(\"PROXY_PASSWORD\")\nasync def fetch_url(crawler, url):\n    try:\n        response = await crawler.arun(url=url)\n        if response.success and response.html:\n            return response.html\n        else:\n            print(f\"⚠️ Failed to fetch: {url}\")\n            return None\n    except Exception as e:", "detail": "map_serp_scraper.scraper.sitemap_scraper", "documentation": {}}, {"label": "get_sitemap_urls", "kind": 2, "importPath": "map_serp_scraper.scraper.sitemap_tool", "description": "map_serp_scraper.scraper.sitemap_tool", "peekOfCode": "def get_sitemap_urls(website_url: str) -> list[str]:\n    \"\"\"\n    Returns all sitemap URLs discovered from the homepage of the given website.\n    If sitemap is not found or any error occurs, returns an empty list.\n    \"\"\"\n    try:\n        tree = sitemap_tree_for_homepage(website_url)\n        return [page.url for page in tree.all_pages()]\n    except Exception:\n        return []", "detail": "map_serp_scraper.scraper.sitemap_tool", "documentation": {}}, {"label": "url<PERSON><PERSON><PERSON>", "kind": 5, "importPath": "map_serp_scraper.scraper.urls", "description": "map_serp_scraper.scraper.urls", "peekOfCode": "urlpatterns = [\n    path(\"search/\", map_search_view),\n]", "detail": "map_serp_scraper.scraper.urls", "documentation": {}}, {"label": "map_search_view", "kind": 2, "importPath": "map_serp_scraper.scraper.views", "description": "map_serp_scraper.scraper.views", "peekOfCode": "def map_search_view(request):\n    if request.method == \"GET\":\n        query = request.GET.get(\"query\")\n        if not query:\n            return JsonResponse({\"success\": False, \"error\": \"Missing 'query' parameter.\"})\n        try:\n            result = scrape_google_maps(query)\n            return JsonResponse(result)\n        except Exception as e:\n            return JsonResponse({\"success\": False, \"error\": str(e)})", "detail": "map_serp_scraper.scraper.views", "documentation": {}}, {"label": "debug_database", "kind": 2, "importPath": "map_serp_scraper.debug_database", "description": "map_serp_scraper.debug_database", "peekOfCode": "def debug_database():\n    \"\"\"Debug database connection and current state.\"\"\"\n    print(\"🔍 Database Debug Information\\n\")\n    # Test 1: Check database connection\n    print(\"1. 🔗 Testing Database Connection\")\n    print(\"-\" * 40)\n    try:\n        total_count = BusinessScrape.objects.count()\n        print(f\"✅ Database connection successful\")\n        print(f\"📊 Total entries in database: {total_count}\")", "detail": "map_serp_scraper.debug_database", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "map_serp_scraper.debug_database", "description": "map_serp_scraper.debug_database", "peekOfCode": "project_root = Path(__file__).parent\nsys.path.insert(0, str(project_root))\n# Setup Django\nos.environ.setdefault('DJANGO_SETTINGS_MODULE', 'map_serp_scraper.settings')\ndjango.setup()\nfrom scraper.models import BusinessScrape\nfrom scraper.db_service import insert_scrape_entry_django\nimport logging\n# Setup logging\nlogging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] [%(name)s] %(message)s')", "detail": "map_serp_scraper.debug_database", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "map_serp_scraper.debug_database", "description": "map_serp_scraper.debug_database", "peekOfCode": "logger = logging.getLogger(__name__)\ndef debug_database():\n    \"\"\"Debug database connection and current state.\"\"\"\n    print(\"🔍 Database Debug Information\\n\")\n    # Test 1: Check database connection\n    print(\"1. 🔗 Testing Database Connection\")\n    print(\"-\" * 40)\n    try:\n        total_count = BusinessScrape.objects.count()\n        print(f\"✅ Database connection successful\")", "detail": "map_serp_scraper.debug_database", "documentation": {}}, {"label": "debug_postgres_database", "kind": 2, "importPath": "map_serp_scraper.debug_postgres", "description": "map_serp_scraper.debug_postgres", "peekOfCode": "def debug_postgres_database():\n    \"\"\"Debug PostgreSQL database connection and current state.\"\"\"\n    print(\"🔍 PostgreSQL Database Debug Information\\n\")\n    # Test 1: Check database connection\n    print(\"1. 🔗 Testing PostgreSQL Database Connection\")\n    print(\"-\" * 50)\n    try:\n        conn = psycopg2.connect(\n            host=os.getenv(\"DB_HOST\"),\n            dbname=os.getenv(\"DB_NAME\"),", "detail": "map_serp_scraper.debug_postgres", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "map_serp_scraper.manage", "description": "map_serp_scraper.manage", "peekOfCode": "def main():\n    \"\"\"Run administrative tasks.\"\"\"\n    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'map_serp_scraper.settings')\n    try:\n        from django.core.management import execute_from_command_line\n    except ImportError as exc:\n        raise ImportError(\n            \"Couldn't import Django. Are you sure it's installed and \"\n            \"available on your PYTHONPATH environment variable? Did you \"\n            \"forget to activate a virtual environment?\"", "detail": "map_serp_scraper.manage", "documentation": {}}, {"label": "migrate_database", "kind": 2, "importPath": "map_serp_scraper.migrate_database", "description": "map_serp_scraper.migrate_database", "peekOfCode": "def migrate_database():\n    \"\"\"\n    Migrate the database to add unique constraint on website column.\n    \"\"\"\n    try:\n        # Connect to database\n        conn = psycopg2.connect(\n            host=os.getenv(\"DB_HOST\"),\n            dbname=os.getenv(\"DB_NAME\"),\n            user=os.getenv(\"DB_USER\"),", "detail": "map_serp_scraper.migrate_database", "documentation": {}}, {"label": "verify_migration", "kind": 2, "importPath": "map_serp_scraper.migrate_database", "description": "map_serp_scraper.migrate_database", "peekOfCode": "def verify_migration():\n    \"\"\"\n    Verify that the migration was successful.\n    \"\"\"\n    try:\n        conn = psycopg2.connect(\n            host=os.getenv(\"DB_HOST\"),\n            dbname=os.getenv(\"DB_NAME\"),\n            user=os.getenv(\"DB_USER\"),\n            password=os.getenv(\"DB_PASS\"),", "detail": "map_serp_scraper.migrate_database", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "map_serp_scraper.migrate_database", "description": "map_serp_scraper.migrate_database", "peekOfCode": "logger = logging.getLogger(__name__)\nload_dotenv()\ndef migrate_database():\n    \"\"\"\n    Migrate the database to add unique constraint on website column.\n    \"\"\"\n    try:\n        # Connect to database\n        conn = psycopg2.connect(\n            host=os.getenv(\"DB_HOST\"),", "detail": "map_serp_scraper.migrate_database", "documentation": {}}, {"label": "test_email_list_functionality", "kind": 2, "importPath": "map_serp_scraper.test_email_list", "description": "map_serp_scraper.test_email_list", "peekOfCode": "def test_email_list_functionality():\n    \"\"\"Test the email list functionality.\"\"\"\n    print(\"🧪 Testing Email List Functionality\\n\")\n    # Test data with email lists\n    test_entries = [\n        {\n            \"name\": \"Tech Company A\",\n            \"rating\": \"4.5\",\n            \"address\": \"123 Tech St, Silicon Valley\",\n            \"website\": \"https://techcompanya.com\",", "detail": "map_serp_scraper.test_email_list", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "map_serp_scraper.test_email_list", "description": "map_serp_scraper.test_email_list", "peekOfCode": "project_root = Path(__file__).parent\nsys.path.insert(0, str(project_root))\n# Setup Django\nos.environ.setdefault('DJANGO_SETTINGS_MODULE', 'map_serp_scraper.settings')\ndjango.setup()\nfrom scraper.db_service import insert_scrape_entry_django, bulk_insert_scrape_entries\nfrom scraper.models import BusinessScrape\ndef test_email_list_functionality():\n    \"\"\"Test the email list functionality.\"\"\"\n    print(\"🧪 Testing Email List Functionality\\n\")", "detail": "map_serp_scraper.test_email_list", "documentation": {}}, {"label": "test_phone_uniqueness", "kind": 2, "importPath": "map_serp_scraper.test_phone_uniqueness", "description": "map_serp_scraper.test_phone_uniqueness", "peekOfCode": "def test_phone_uniqueness():\n    \"\"\"Test the phone number uniqueness functionality.\"\"\"\n    print(\"🧪 Testing Phone Number Uniqueness Functionality\\n\")\n    # Test data with duplicate phone numbers\n    test_entries = [\n        {\n            \"name\": \"Company A\",\n            \"rating\": \"4.5\",\n            \"address\": \"123 Main St, City A\",\n            \"website\": \"https://companya.com\",", "detail": "map_serp_scraper.test_phone_uniqueness", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "map_serp_scraper.test_phone_uniqueness", "description": "map_serp_scraper.test_phone_uniqueness", "peekOfCode": "project_root = Path(__file__).parent\nsys.path.insert(0, str(project_root))\n# Setup Django\nos.environ.setdefault('DJANGO_SETTINGS_MODULE', 'map_serp_scraper.settings')\ndjango.setup()\nfrom scraper.db_service import insert_scrape_entry_django, bulk_insert_scrape_entries\nfrom scraper.models import BusinessScrape\ndef test_phone_uniqueness():\n    \"\"\"Test the phone number uniqueness functionality.\"\"\"\n    print(\"🧪 Testing Phone Number Uniqueness Functionality\\n\")", "detail": "map_serp_scraper.test_phone_uniqueness", "documentation": {}}, {"label": "url", "kind": 5, "importPath": "map_serp_scraper.test_site_scraper", "description": "map_serp_scraper.test_site_scraper", "peekOfCode": "url = \"https://www.lauruslabs.com\"\nresult = scrape_site_contact_info(url)\nprint(\"\\n📦 Scraped Contact Info:\")\nprint(json.dumps(result, indent=2))", "detail": "map_serp_scraper.test_site_scraper", "documentation": {}}, {"label": "result", "kind": 5, "importPath": "map_serp_scraper.test_site_scraper", "description": "map_serp_scraper.test_site_scraper", "peekOfCode": "result = scrape_site_contact_info(url)\nprint(\"\\n📦 Scraped Contact Info:\")\nprint(json.dumps(result, indent=2))", "detail": "map_serp_scraper.test_site_scraper", "documentation": {}}, {"label": "urls", "kind": 5, "importPath": "map_serp_scraper.test_sitemap_scraper", "description": "map_serp_scraper.test_sitemap_scraper", "peekOfCode": "urls = scrape_sitemap(\"https://www.lauruslabs.com\")\nprint(\"\\n🗺️ Sitemap URLs:\")\nfor u in urls:\n    print(\"🔗\", u)", "detail": "map_serp_scraper.test_sitemap_scraper", "documentation": {}}, {"label": "urls", "kind": 5, "importPath": "map_serp_scraper.test_sitemap_tool", "description": "map_serp_scraper.test_sitemap_tool", "peekOfCode": "urls = get_sitemap_urls(\"https://www.affectiontag.com\")\nif urls:\n    print(f\"\\n🗺️ Found {len(urls)} sitemap URLs:\")\n    for u in urls:\n        print(\"🔗\", u)\nelse:\n    print(\"\\n❌ No sitemap URLs found.\")", "detail": "map_serp_scraper.test_sitemap_tool", "documentation": {}}, {"label": "test_unique_constraint", "kind": 2, "importPath": "map_serp_scraper.test_unique_website", "description": "map_serp_scraper.test_unique_website", "peekOfCode": "def test_unique_constraint():\n    \"\"\"Test the unique website constraint functionality.\"\"\"\n    print(\"🧪 Testing Unique Website Constraint Functionality\\n\")\n    # Test data with duplicate websites\n    test_entries = [\n        {\n            \"name\": \"Example Company 1\",\n            \"rating\": \"4.5\",\n            \"address\": \"123 Main St, City, State\",\n            \"website\": \"https://example.com\",", "detail": "map_serp_scraper.test_unique_website", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "map_serp_scraper.test_unique_website", "description": "map_serp_scraper.test_unique_website", "peekOfCode": "project_root = Path(__file__).parent\nsys.path.insert(0, str(project_root))\n# Setup Django\nos.environ.setdefault('DJANGO_SETTINGS_MODULE', 'map_serp_scraper.settings')\ndjango.setup()\nfrom scraper.db_service import (\n    insert_scrape_entry, \n    insert_scrape_entry_django, \n    bulk_insert_scrape_entries,\n    website_exists", "detail": "map_serp_scraper.test_unique_website", "documentation": {}}]