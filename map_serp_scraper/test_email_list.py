#!/usr/bin/env python3
"""
Test script to demonstrate email list functionality.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'map_serp_scraper.settings')
django.setup()

from scraper.db_service import insert_scrape_entry_django, bulk_insert_scrape_entries
from scraper.models import BusinessScrape


def test_email_list_functionality():
    """Test the email list functionality."""
    
    print("🧪 Testing Email List Functionality\n")
    
    # Test data with email lists
    test_entries = [
        {
            "name": "Tech Company A",
            "rating": "4.5",
            "address": "123 Tech St, Silicon Valley",
            "website": "https://techcompanya.com",
            "phone": "******-0001",
            "emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            "category": "Technology",
            "source_query": "tech companies test"
        },
        {
            "name": "Marketing Agency B",
            "rating": "4.2",
            "address": "456 Marketing Ave, New York",
            "website": "https://marketingb.com",
            "phone": "******-0002",
            "emails": ["<EMAIL>", "<EMAIL>"],
            "category": "Marketing",
            "source_query": "marketing agencies test"
        },
        {
            "name": "Consulting Firm C",
            "rating": "4.8",
            "address": "789 Business Blvd, Chicago",
            "website": "https://consultingc.com",
            "phone": "******-0003",
            "emails": ["<EMAIL>"],  # Single email in list
            "category": "Consulting",
            "source_query": "consulting firms test"
        },
        {
            "name": "Software Company D",
            "rating": "4.0",
            "address": "321 Code St, Austin",
            "website": "https://softwared.com",
            "phone": "******-0004",
            "emails": [],  # Empty email list
            "category": "Software",
            "source_query": "software companies test"
        }
    ]
    
    print("📋 Test Data:")
    for i, entry in enumerate(test_entries, 1):
        emails_str = ", ".join(entry['emails']) if entry['emails'] else "No emails"
        print(f"  {i}. {entry['name']} - {entry['website']}")
        print(f"     Emails: {emails_str}")
    print()
    
    # Clean up existing test data
    BusinessScrape.objects.filter(source_query__contains="test").delete()
    
    # Test 1: Insert entries with email lists
    print("🔧 Test 1: Inserting Entries with Email Lists")
    print("-" * 50)
    
    for i, entry in enumerate(test_entries, 1):
        print(f"Inserting entry {i}: {entry['name']}")
        success, created, message = insert_scrape_entry_django(entry)
        print(f"  Result: Success={success}, Created={created}")
        if created:
            # Verify the emails were saved correctly
            instance = BusinessScrape.objects.get(website=entry['website'])
            saved_emails = instance.get_emails()
            print(f"  Saved emails: {saved_emails}")
        print()
    
    # Test 2: Test Django model email methods
    print("🔧 Test 2: Testing Django Model Email Methods")
    print("-" * 50)
    
    # Get the first test entry
    test_instance = BusinessScrape.objects.filter(source_query__contains="test").first()
    if test_instance:
        print(f"Testing with: {test_instance.name}")
        
        # Test get_emails()
        current_emails = test_instance.get_emails()
        print(f"Current emails: {current_emails}")
        
        # Test add_email()
        test_instance.add_email("<EMAIL>")
        test_instance.save()
        updated_emails = test_instance.get_emails()
        print(f"After adding email: {updated_emails}")
        
        # Test add_emails()
        test_instance.add_emails(["<EMAIL>", "<EMAIL>"])
        test_instance.save()
        final_emails = test_instance.get_emails()
        print(f"After adding bulk emails: {final_emails}")
        
        # Test duplicate handling
        test_instance.add_email("<EMAIL>")  # Duplicate
        test_instance.save()
        no_duplicate_emails = test_instance.get_emails()
        print(f"After adding duplicate (should be same): {no_duplicate_emails}")
        
        print()
    
    # Test 3: Bulk insert with mixed email formats
    print("🔧 Test 3: Bulk Insert with Mixed Email Formats")
    print("-" * 50)
    
    bulk_test_entries = [
        {
            "name": "Bulk Test 1",
            "website": "https://bulktest1.com",
            "emails": ["<EMAIL>", "<EMAIL>"],
            "source_query": "bulk email test"
        },
        {
            "name": "Bulk Test 2",
            "website": "https://bulktest2.com",
            "emails": "<EMAIL>",  # String instead of list
            "source_query": "bulk email test"
        },
        {
            "name": "Bulk Test 3",
            "website": "https://bulktest3.com",
            # No emails field
            "source_query": "bulk email test"
        }
    ]
    
    results = bulk_insert_scrape_entries(bulk_test_entries)
    print(f"Bulk insert results:")
    print(f"  Total entries: {results['total_entries']}")
    print(f"  Inserted: {results['inserted']}")
    print(f"  Skipped: {results['skipped']}")
    print(f"  Errors: {results['errors']}")
    print()
    
    # Test 4: Verify database state
    print("📊 Test 4: Database State Verification")
    print("-" * 50)
    
    test_entries_in_db = BusinessScrape.objects.filter(source_query__contains="test")
    print(f"Total test entries in database: {test_entries_in_db.count()}")
    
    print("\nEntries with emails:")
    for entry in test_entries_in_db.filter(email_list__isnull=False).exclude(email_list=""):
        emails = entry.get_emails()
        print(f"  - {entry.name}: {emails} (raw: {entry.email_list})")
    
    print("\nEntries without emails:")
    for entry in test_entries_in_db.filter(email_list__isnull=True) | test_entries_in_db.filter(email_list=""):
        print(f"  - {entry.name}: No emails")
    
    print()
    
    # Test 5: Email list manipulation
    print("🔧 Test 5: Email List Manipulation")
    print("-" * 50)
    
    if test_entries_in_db.exists():
        test_entry = test_entries_in_db.first()
        print(f"Manipulating emails for: {test_entry.name}")
        
        # Set new email list
        new_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        test_entry.set_emails(new_emails)
        test_entry.save()
        print(f"Set new emails: {test_entry.get_emails()}")
        
        # Clear emails
        test_entry.set_emails([])
        test_entry.save()
        print(f"Cleared emails: {test_entry.get_emails()}")
        
        # Add emails back
        test_entry.add_emails(["<EMAIL>", "<EMAIL>"])
        test_entry.save()
        print(f"Restored emails: {test_entry.get_emails()}")
    
    print("\n✅ All email list tests completed!")
    
    # Cleanup (optional)
    print("\n🧹 Cleanup (removing test data)")
    deleted_count = BusinessScrape.objects.filter(source_query__contains="test").delete()[0]
    print(f"  Removed {deleted_count} test entries")


if __name__ == "__main__":
    test_email_list_functionality()
