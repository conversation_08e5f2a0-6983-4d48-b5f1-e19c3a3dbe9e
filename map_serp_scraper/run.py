import logging
from scraper.run_pipeline import run_pipeline

# Configure logging to show INFO level messages
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

if __name__ == "__main__":
    print("🚀 Starting Lead Generation Pipeline...")
    result = run_pipeline("Pharma company", "Hyderabad")
    print("\n🔚 Pipeline finished:")
    print(result)
