#!/usr/bin/env python3
"""
Test script to demonstrate phone number uniqueness functionality.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'map_serp_scraper.settings')
django.setup()

from scraper.db_service import insert_scrape_entry_django, bulk_insert_scrape_entries
from scraper.models import BusinessScrape


def test_phone_uniqueness():
    """Test the phone number uniqueness functionality."""
    
    print("🧪 Testing Phone Number Uniqueness Functionality\n")
    
    # Test data with duplicate phone numbers
    test_entries = [
        {
            "name": "Company A",
            "rating": "4.5",
            "address": "123 Main St, City A",
            "website": "https://companya.com",
            "phone": "******-1234",
            "emails": ["<EMAIL>"],
            "category": "Technology",
            "source_query": "phone uniqueness test"
        },
        {
            "name": "Company B",
            "rating": "4.2",
            "address": "456 Oak Ave, City B",
            "website": "https://companyb.com",
            "phone": "******-1234",  # Same phone as Company A
            "emails": ["<EMAIL>"],
            "category": "Marketing",
            "source_query": "phone uniqueness test"
        },
        {
            "name": "Company C",
            "rating": "4.8",
            "address": "789 Pine St, City C",
            "website": "https://companyc.com",
            "phone": "******-5678",  # Different phone
            "emails": ["<EMAIL>"],
            "category": "Consulting",
            "source_query": "phone uniqueness test"
        },
        {
            "name": "Company D",
            "rating": "4.0",
            "address": "321 Elm St, City D",
            "website": "https://companyd.com",
            "phone": "******-5678",  # Same phone as Company C
            "emails": ["<EMAIL>"],
            "category": "Services",
            "source_query": "phone uniqueness test"
        },
        {
            "name": "Company E",
            "rating": "4.3",
            "address": "654 Maple Dr, City E",
            "website": "https://companye.com",
            "phone": "******-9999",  # Unique phone
            "emails": ["<EMAIL>"],
            "category": "Software",
            "source_query": "phone uniqueness test"
        }
    ]
    
    print("📋 Test Data:")
    for i, entry in enumerate(test_entries, 1):
        print(f"  {i}. {entry['name']} - Phone: {entry['phone']} - Website: {entry['website']}")
    print()
    
    # Clean up existing test data
    BusinessScrape.objects.filter(source_query="phone uniqueness test").delete()
    
    # Test 1: Insert entries with duplicate phone numbers
    print("🔧 Test 1: Inserting Entries with Duplicate Phone Numbers")
    print("-" * 60)
    
    results = []
    for i, entry in enumerate(test_entries, 1):
        print(f"Inserting entry {i}: {entry['name']} (Phone: {entry['phone']})")
        success, created, message = insert_scrape_entry_django(entry)
        results.append((entry, success, created, message))
        print(f"  Result: Success={success}, Created={created}")
        print(f"  Message: {message}")
        print()
    
    # Test 2: Verify what was actually inserted
    print("📊 Test 2: Database State Verification")
    print("-" * 60)
    
    inserted_entries = BusinessScrape.objects.filter(source_query="phone uniqueness test").order_by('created_at')
    print(f"Total entries inserted: {inserted_entries.count()}")
    print("\nInserted entries:")
    for entry in inserted_entries:
        print(f"  - {entry.name}: Phone={entry.phone}, Website={entry.website}")
    
    # Test 3: Check phone number uniqueness
    print(f"\n🔍 Test 3: Phone Number Uniqueness Check")
    print("-" * 60)
    
    unique_phones = set()
    duplicate_phones = set()
    
    for entry in inserted_entries:
        if entry.phone in unique_phones:
            duplicate_phones.add(entry.phone)
        else:
            unique_phones.add(entry.phone)
    
    print(f"Unique phone numbers found: {len(unique_phones)}")
    print(f"Duplicate phone numbers found: {len(duplicate_phones)}")
    
    if duplicate_phones:
        print("❌ FAILED: Found duplicate phone numbers in database!")
        for phone in duplicate_phones:
            entries_with_phone = inserted_entries.filter(phone=phone)
            print(f"  Phone {phone} appears {entries_with_phone.count()} times:")
            for entry in entries_with_phone:
                print(f"    - {entry.name} ({entry.website})")
    else:
        print("✅ SUCCESS: All phone numbers are unique!")
    
    # Test 4: Test bulk insert with duplicates
    print(f"\n🚀 Test 4: Bulk Insert with Mixed Duplicates")
    print("-" * 60)
    
    bulk_test_entries = [
        {
            "name": "Bulk Test 1",
            "website": "https://bulktest1.com",
            "phone": "******-BULK1",
            "source_query": "phone uniqueness test"
        },
        {
            "name": "Bulk Test 2",
            "website": "https://bulktest2.com",
            "phone": "******-1234",  # Duplicate of existing phone
            "source_query": "phone uniqueness test"
        },
        {
            "name": "Bulk Test 3",
            "website": "https://bulktest3.com",
            "phone": "******-BULK3",
            "source_query": "phone uniqueness test"
        }
    ]
    
    bulk_results = bulk_insert_scrape_entries(bulk_test_entries)
    print(f"Bulk insert results:")
    print(f"  Total entries: {bulk_results['total_entries']}")
    print(f"  Inserted: {bulk_results['inserted']}")
    print(f"  Skipped: {bulk_results['skipped']}")
    print(f"  Errors: {bulk_results['errors']}")
    
    if bulk_results['skipped'] > 0:
        print(f"  Skipped entries: {bulk_results.get('skipped_websites', [])}")
    
    # Test 5: Final verification
    print(f"\n📈 Test 5: Final Database State")
    print("-" * 60)
    
    final_entries = BusinessScrape.objects.filter(source_query="phone uniqueness test")
    final_count = final_entries.count()
    unique_phone_count = final_entries.values('phone').distinct().count()
    unique_website_count = final_entries.values('website').distinct().count()
    
    print(f"Final statistics:")
    print(f"  Total entries: {final_count}")
    print(f"  Unique phone numbers: {unique_phone_count}")
    print(f"  Unique websites: {unique_website_count}")
    
    # Verify constraints are working
    if final_count == unique_phone_count == unique_website_count:
        print("✅ SUCCESS: Both phone and website uniqueness constraints are working!")
    else:
        print("❌ ISSUE: Uniqueness constraints may not be working properly")
    
    print("\n📋 Final entries in database:")
    for entry in final_entries.order_by('created_at'):
        print(f"  - {entry.name}: Phone={entry.phone}, Website={entry.website}")
    
    print("\n✅ All phone uniqueness tests completed!")
    
    # Cleanup (optional)
    print("\n🧹 Cleanup (removing test data)")
    deleted_count = BusinessScrape.objects.filter(source_query="phone uniqueness test").delete()[0]
    print(f"  Removed {deleted_count} test entries")


if __name__ == "__main__":
    test_phone_uniqueness()
