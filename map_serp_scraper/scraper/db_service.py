import os
import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import logging

load_dotenv()
logger = logging.getLogger("pipeline")


def insert_scrape_entry(entry: dict):
    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            dbname=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASS"),
            port=os.getenv("DB_PORT")
        )
        cur = conn.cursor()

        # Ensure table exists
        cur.execute("""
            CREATE TABLE IF NOT EXISTS business_scrapes (
                id SERIAL PRIMARY KEY,
                name TEXT,
                rating TEXT,
                address TEXT,
                website TEXT,
                phone TEXT,
                category TEXT,
                source_query TEXT
            );
        """)

        # Insert one entry
        cur.execute(sql.SQL("""
            INSERT INTO business_scrapes
            (name, rating, address, website, phone, category, source_query)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """), (
            entry.get("name"),
            entry.get("rating"),
            entry.get("address"),
            entry.get("website"),
            entry.get("phone"),
            entry.get("category"),
            entry.get("source_query")
        ))

        conn.commit()
        cur.close()
        conn.close()

        logger.info(
            f"📝 Inserted business: {entry.get('name', 'N/A')} from query: {entry.get('source_query', 'N/A')}")

    except Exception as e:
        logger.error(
            f"❌ Failed to insert entry from query '{entry.get('source_query', 'unknown')}': {e}", exc_info=True)
