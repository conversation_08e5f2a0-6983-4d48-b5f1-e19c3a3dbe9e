import os
import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import logging

load_dotenv()
logger = logging.getLogger("pipeline")


def website_exists(website_url: str) -> bool:
    """
    Check if a website URL already exists in the database.

    Args:
        website_url (str): The website URL to check

    Returns:
        bool: True if website exists, False otherwise
    """
    if not website_url:
        return False

    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            dbname=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASS"),
            port=os.getenv("DB_PORT")
        )
        cur = conn.cursor()

        cur.execute(
            "SELECT id FROM business_scrapes WHERE website = %s", (website_url,))
        exists = cur.fetchone() is not None

        cur.close()
        conn.close()

        return exists

    except Exception as e:
        logger.error(f"❌ Error checking if website exists: {e}")
        return False


def insert_scrape_entry(entry: dict):
    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            dbname=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASS"),
            port=os.getenv("DB_PORT")
        )
        cur = conn.cursor()

        # Ensure table exists with unique constraint on website
        cur.execute("""
            CREATE TABLE IF NOT EXISTS business_scrapes (
                id SERIAL PRIMARY KEY,
                name TEXT,
                rating TEXT,
                address TEXT,
                website TEXT UNIQUE,
                phone TEXT,
                category TEXT,
                source_query TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

        # Create index on website for faster lookups
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_business_scrapes_website
            ON business_scrapes(website);
        """)

        # Check if website already exists
        website_url = entry.get("website")
        if website_url:
            cur.execute(
                "SELECT id FROM business_scrapes WHERE website = %s", (website_url,))
            existing_entry = cur.fetchone()

            if existing_entry:
                logger.info(
                    f"🔄 Website already exists in database: {website_url}. Skipping insertion.")
                cur.close()
                conn.close()
                return False  # Indicate that entry was not inserted

        # Insert new entry using ON CONFLICT to handle race conditions
        cur.execute(sql.SQL("""
            INSERT INTO business_scrapes
            (name, rating, address, website, phone, category, source_query)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (website) DO NOTHING
            RETURNING id
        """), (
            entry.get("name"),
            entry.get("rating"),
            entry.get("address"),
            entry.get("website"),
            entry.get("phone"),
            entry.get("category"),
            entry.get("source_query")
        ))

        # Check if the insert was successful
        inserted_row = cur.fetchone()
        if not inserted_row:
            logger.info(
                f"🔄 Website already exists (conflict): {website_url}. Skipping insertion.")
            cur.close()
            conn.close()
            return False

        conn.commit()
        cur.close()
        conn.close()

        logger.info(
            f"📝 Successfully inserted business: {entry.get('name', 'N/A')} with website: {website_url} from query: {entry.get('source_query', 'N/A')}")
        return True  # Indicate successful insertion

    except psycopg2.IntegrityError as e:
        if "duplicate key value violates unique constraint" in str(e):
            logger.info(
                f"🔄 Website already exists: {entry.get('website')}. Skipping insertion.")
            return False
        else:
            logger.error(f"❌ Database integrity error: {e}", exc_info=True)
            return False
    except Exception as e:
        logger.error(
            f"❌ Failed to insert entry from query '{entry.get('source_query', 'unknown')}': {e}", exc_info=True)
        return False


def insert_scrape_entry_django(entry: dict):
    """
    Django ORM-based function to insert scrape entry with unique website constraint.

    Args:
        entry (dict): Dictionary containing business information

    Returns:
        tuple: (success: bool, created: bool, message: str)
    """
    try:
        from .models import BusinessScrape

        website_url = entry.get("website")
        if not website_url:
            logger.warning("⚠️ No website URL provided, skipping entry")
            return False, False, "No website URL provided"

        # Check if website already exists
        if BusinessScrape.website_exists(website_url):
            logger.info(
                f"🔄 Website already exists in database: {website_url}. Skipping insertion.")
            return False, False, f"Website already exists: {website_url}"

        # Create new entry
        instance, created = BusinessScrape.create_if_not_exists(
            name=entry.get("name"),
            rating=entry.get("rating"),
            address=entry.get("address"),
            website=website_url,
            phone=entry.get("phone"),
            category=entry.get("category"),
            source_query=entry.get("source_query")
        )

        if created:
            logger.info(
                f"📝 Successfully inserted business: {entry.get('name', 'N/A')} with website: {website_url} (ID: {instance.id})")
            return True, True, f"Successfully inserted: {website_url}"
        else:
            logger.info(
                f"🔄 Website already exists: {website_url}. Skipping insertion.")
            return False, False, f"Website already exists: {website_url}"

    except Exception as e:
        logger.error(
            f"❌ Failed to insert entry using Django ORM: {e}", exc_info=True)
        return False, False, f"Error: {str(e)}"


def bulk_insert_scrape_entries(entries: list):
    """
    Bulk insert multiple scrape entries, skipping duplicates.

    Args:
        entries (list): List of dictionaries containing business information

    Returns:
        dict: Summary of insertion results
    """
    results = {
        "total_entries": len(entries),
        "inserted": 0,
        "skipped": 0,
        "errors": 0,
        "skipped_websites": [],
        "error_messages": []
    }

    for entry in entries:
        try:
            success, created, message = insert_scrape_entry_django(entry)
            if success and created:
                results["inserted"] += 1
            elif not created:
                results["skipped"] += 1
                results["skipped_websites"].append(entry.get("website"))
            else:
                results["errors"] += 1
                results["error_messages"].append(message)
        except Exception as e:
            results["errors"] += 1
            results["error_messages"].append(str(e))
            logger.error(f"❌ Error processing entry: {e}")

    logger.info(
        f"📊 Bulk insert completed: {results['inserted']} inserted, {results['skipped']} skipped, {results['errors']} errors")
    return results
