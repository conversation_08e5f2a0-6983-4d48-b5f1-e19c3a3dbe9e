import os
import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import logging

load_dotenv()
logger = logging.getLogger("pipeline")


def website_exists(website_url: str) -> bool:
    """
    Check if a website URL already exists in the database.

    Args:
        website_url (str): The website URL to check

    Returns:
        bool: True if website exists, False otherwise
    """
    if not website_url:
        return False

    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            dbname=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASS"),
            port=os.getenv("DB_PORT")
        )
        cur = conn.cursor()

        cur.execute(
            "SELECT id FROM business_scrapes WHERE website = %s", (website_url,))
        exists = cur.fetchone() is not None

        cur.close()
        conn.close()

        return exists

    except Exception as e:
        logger.error(f"❌ Error checking if website exists: {e}")
        return False


def insert_scrape_entry(entry: dict):
    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            dbname=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASS"),
            port=os.getenv("DB_PORT")
        )
        cur = conn.cursor()

        # Ensure table exists (basic structure)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS business_scrapes (
                id SERIAL PRIMARY KEY,
                name TEXT,
                rating TEXT,
                address TEXT,
                website TEXT,
                phone TEXT,
                email_list TEXT,
                category TEXT,
                source_query TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

        # Create index on website for faster lookups (if not exists)
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_business_scrapes_website
            ON business_scrapes(website);
        """)

        # Check if website or phone already exists
        website_url = entry.get("website")
        phone_number = entry.get("phone")

        # Check website uniqueness
        if website_url:
            cur.execute(
                "SELECT id, name FROM business_scrapes WHERE website = %s", (website_url,))
            existing_entry = cur.fetchone()

            if existing_entry:
                logger.info(
                    f"🔄 Website already exists in database: {website_url} (Business: {existing_entry[1]}). Skipping insertion.")
                cur.close()
                conn.close()
                return False  # Indicate that entry was not inserted

        # Check phone uniqueness
        if phone_number:
            cur.execute(
                "SELECT id, name, website FROM business_scrapes WHERE phone = %s", (phone_number,))
            existing_phone = cur.fetchone()

            if existing_phone:
                logger.info(
                    f"🔄 Phone number already exists in database: {phone_number} (Business: {existing_phone[1]}, Website: {existing_phone[2]}). Skipping insertion.")
                cur.close()
                conn.close()
                return False  # Indicate that entry was not inserted

        # Prepare email list (convert list to semicolon-separated string)
        email_list_str = ""
        if "emails" in entry and entry["emails"]:
            if isinstance(entry["emails"], list):
                # Clean and deduplicate emails
                clean_emails = []
                seen = set()
                for email in entry["emails"]:
                    email = str(email).strip().lower()
                    if email and email not in seen:
                        clean_emails.append(email)
                        seen.add(email)
                email_list_str = ";".join(clean_emails)
            elif isinstance(entry["emails"], str):
                # Handle single email string
                email = entry["emails"].strip().lower()
                if email:
                    email_list_str = email
            else:
                email_list_str = str(entry["emails"])

        # Try to insert with ON CONFLICT first (if constraints exist)
        try:
            # First try with both website and phone constraints
            cur.execute(sql.SQL("""
                INSERT INTO business_scrapes
                (name, rating, address, website, phone, email_list, category, source_query)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (website) DO NOTHING
                RETURNING id
            """), (
                entry.get("name"),
                entry.get("rating"),
                entry.get("address"),
                entry.get("website"),
                entry.get("phone"),
                email_list_str,
                entry.get("category"),
                entry.get("source_query")
            ))

            # Check if insert was successful
            inserted_row = cur.fetchone()
            if not inserted_row:
                # Try with phone conflict check
                cur.execute(sql.SQL("""
                    INSERT INTO business_scrapes
                    (name, rating, address, website, phone, email_list, category, source_query)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (phone) DO NOTHING
                    RETURNING id
                """), (
                    entry.get("name"),
                    entry.get("rating"),
                    entry.get("address"),
                    entry.get("website"),
                    entry.get("phone"),
                    email_list_str,
                    entry.get("category"),
                    entry.get("source_query")
                ))
                inserted_row = cur.fetchone()

            # Check if the insert was successful
            inserted_row = cur.fetchone()
            if not inserted_row:
                logger.info(
                    f"🔄 Website already exists (conflict): {website_url}. Skipping insertion.")
                cur.close()
                conn.close()
                return False

        except psycopg2.errors.InvalidColumnReference:
            # Unique constraint doesn't exist, fall back to regular insert
            logger.warning(
                "⚠️ Unique constraint not found, using regular insert")
            cur.execute(sql.SQL("""
                INSERT INTO business_scrapes
                (name, rating, address, website, phone, email_list, category, source_query)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """), (
                entry.get("name"),
                entry.get("rating"),
                entry.get("address"),
                entry.get("website"),
                entry.get("phone"),
                email_list_str,
                entry.get("category"),
                entry.get("source_query")
            ))

            inserted_row = cur.fetchone()

        conn.commit()
        cur.close()
        conn.close()

        logger.info(
            f"📝 Successfully inserted business: {entry.get('name', 'N/A')} with website: {website_url} from query: {entry.get('source_query', 'N/A')}")
        return True  # Indicate successful insertion

    except psycopg2.IntegrityError as e:
        if "duplicate key value violates unique constraint" in str(e):
            logger.info(
                f"🔄 Website already exists: {entry.get('website')}. Skipping insertion.")
            return False
        else:
            logger.error(f"❌ Database integrity error: {e}", exc_info=True)
            return False
    except Exception as e:
        logger.error(
            f"❌ Failed to insert entry from query '{entry.get('source_query', 'unknown')}': {e}", exc_info=True)
        return False


def insert_scrape_entry_django(entry: dict):
    """
    Django ORM-based function to insert scrape entry with unique website constraint.

    Args:
        entry (dict): Dictionary containing business information

    Returns:
        tuple: (success: bool, created: bool, message: str)
    """
    try:
        from .models import BusinessScrape

        website_url = entry.get("website")
        phone_number = entry.get("phone")

        if not website_url and not phone_number:
            logger.warning(
                "⚠️ No website URL or phone number provided, skipping entry")
            return False, False, "No website URL or phone number provided"

        # Check if website already exists
        if website_url and BusinessScrape.website_exists(website_url):
            logger.info(
                f"🔄 Website already exists in database: {website_url}. Skipping insertion.")
            return False, False, f"Website already exists: {website_url}"

        # Check if phone already exists
        if phone_number:
            existing_phone = BusinessScrape.objects.filter(
                phone=phone_number).first()
            if existing_phone:
                logger.info(
                    f"🔄 Phone number already exists in database: {phone_number} (Business: {existing_phone.name}). Skipping insertion.")
                return False, False, f"Phone number already exists: {phone_number}"

        # Prepare email list
        emails = entry.get("emails", [])

        # Create new entry
        instance, created = BusinessScrape.create_if_not_exists(
            name=entry.get("name"),
            rating=entry.get("rating"),
            address=entry.get("address"),
            website=website_url,
            phone=entry.get("phone"),
            category=entry.get("category"),
            source_query=entry.get("source_query")
        )

        # Set emails if the instance was created or if we want to update emails
        if created and emails:
            instance.set_emails(emails)
            instance.save()

        if created:
            logger.info(
                f"📝 Successfully inserted business: {entry.get('name', 'N/A')} with website: {website_url} (ID: {instance.id})")
            return True, True, f"Successfully inserted: {website_url}"
        else:
            logger.info(
                f"🔄 Website already exists: {website_url}. Skipping insertion.")
            return False, False, f"Website already exists: {website_url}"

    except Exception as e:
        logger.error(
            f"❌ Failed to insert entry using Django ORM: {e}", exc_info=True)
        return False, False, f"Error: {str(e)}"


def bulk_insert_scrape_entries(entries: list):
    """
    Bulk insert multiple scrape entries, skipping duplicates.

    Args:
        entries (list): List of dictionaries containing business information

    Returns:
        dict: Summary of insertion results
    """
    results = {
        "total_entries": len(entries),
        "inserted": 0,
        "skipped": 0,
        "errors": 0,
        "skipped_websites": [],
        "error_messages": []
    }

    for entry in entries:
        try:
            success, created, message = insert_scrape_entry_django(entry)
            if success and created:
                results["inserted"] += 1
            elif not created:
                results["skipped"] += 1
                results["skipped_websites"].append(entry.get("website"))
            else:
                results["errors"] += 1
                results["error_messages"].append(message)
        except Exception as e:
            results["errors"] += 1
            results["error_messages"].append(str(e))
            logger.error(f"❌ Error processing entry: {e}")

    logger.info(
        f"📊 Bulk insert completed: {results['inserted']} inserted, {results['skipped']} skipped, {results['errors']} errors")
    return results
