import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()


def test_db_connection() -> bool:
    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            database=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASS"),
            port=os.getenv("DB_PORT")
        )
        conn.close()
        print("✅ PostgreSQL connection successful.")
        return True
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False
