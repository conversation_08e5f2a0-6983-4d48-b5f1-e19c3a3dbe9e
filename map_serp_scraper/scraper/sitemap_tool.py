from usp.tree import sitemap_tree_for_homepage


def get_sitemap_urls(website_url: str) -> list[str]:
    """
    Returns all sitemap URLs discovered from the homepage of the given website.
    If sitemap is not found or any error occurs, returns an empty list.
    """
    try:
        tree = sitemap_tree_for_homepage(website_url)
        return [page.url for page in tree.all_pages()]
    except Exception:
        return []
