from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from scraper.map_scraper_service import scrape_google_maps


@csrf_exempt
def map_search_view(request):
    if request.method == "GET":
        query = request.GET.get("query")
        if not query:
            return JsonResponse({"success": False, "error": "Missing 'query' parameter."})

        try:
            result = scrape_google_maps(query)
            return JsonResponse(result)
        except Exception as e:
            return JsonResponse({"success": False, "error": str(e)})

# Create your views here.
