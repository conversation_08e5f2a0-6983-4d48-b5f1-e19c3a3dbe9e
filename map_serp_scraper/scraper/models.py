from django.db import models
from django.core.validators import URLValidator
from django.core.exceptions import ValidationError


class BusinessScrape(models.Model):
    """
    Model to store scraped business information with unique website constraint.
    """
    name = models.TextField(blank=True, null=True, help_text="Business name")
    rating = models.CharField(
        max_length=10, blank=True, null=True, help_text="Business rating")
    address = models.TextField(
        blank=True, null=True, help_text="Business address")
    website = models.URLField(
        unique=True,
        blank=True,
        null=True,
        help_text="Business website URL (must be unique)",
        db_index=True
    )
    phone = models.Char<PERSON>ield(
        max_length=50,
        blank=True,
        null=True,
        unique=True,
        help_text="Business phone number (must be unique)",
        db_index=True
    )
    email_list = models.TextField(
        blank=True,
        null=True,
        help_text="List of email addresses separated by semicolons (;)"
    )
    category = models.CharField(
        max_length=200, blank=True, null=True, help_text="Business category")
    source_query = models.TextField(
        blank=True, null=True, help_text="Original search query used")
    created_at = models.DateTimeField(
        auto_now_add=True, help_text="When the record was created")
    updated_at = models.DateTimeField(
        auto_now=True, help_text="When the record was last updated")

    class Meta:
        db_table = 'business_scrapes'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['website']),
            models.Index(fields=['created_at']),
            models.Index(fields=['category']),
        ]

    def __str__(self):
        return f"{self.name or 'Unknown'} - {self.website or 'No website'}"

    def clean(self):
        """Validate the model data"""
        if self.website:
            # Normalize website URL
            if not self.website.startswith(('http://', 'https://')):
                self.website = f"https://{self.website}"

            # Validate URL format
            validator = URLValidator()
            try:
                validator(self.website)
            except ValidationError:
                raise ValidationError({'website': 'Enter a valid URL.'})

    def save(self, *args, **kwargs):
        """Override save to ensure validation"""
        self.clean()
        super().save(*args, **kwargs)

    def get_emails(self):
        """
        Get email list as a Python list.

        Returns:
            list: List of email addresses
        """
        if not self.email_list:
            return []
        return [email.strip() for email in self.email_list.split(';') if email.strip()]

    def set_emails(self, emails):
        """
        Set email list from a Python list.

        Args:
            emails (list): List of email addresses
        """
        if not emails:
            self.email_list = ''
        else:
            # Clean and deduplicate emails
            clean_emails = []
            seen = set()
            for email in emails:
                email = email.strip().lower()
                if email and email not in seen:
                    clean_emails.append(email)
                    seen.add(email)
            self.email_list = ';'.join(clean_emails)

    def add_email(self, email):
        """
        Add a single email to the list.

        Args:
            email (str): Email address to add
        """
        current_emails = self.get_emails()
        email = email.strip().lower()
        if email and email not in current_emails:
            current_emails.append(email)
            self.set_emails(current_emails)

    def add_emails(self, emails):
        """
        Add multiple emails to the list.

        Args:
            emails (list): List of email addresses to add
        """
        current_emails = self.get_emails()
        for email in emails:
            email = email.strip().lower()
            if email and email not in current_emails:
                current_emails.append(email)
        self.set_emails(current_emails)

    @classmethod
    def website_exists(cls, website_url: str) -> bool:
        """
        Check if a website URL already exists in the database.

        Args:
            website_url (str): The website URL to check

        Returns:
            bool: True if website exists, False otherwise
        """
        if not website_url:
            return False

        # Normalize URL for comparison
        if not website_url.startswith(('http://', 'https://')):
            website_url = f"https://{website_url}"

        return cls.objects.filter(website=website_url).exists()

    @classmethod
    def create_if_not_exists(cls, **kwargs):
        """
        Create a new business scrape entry only if the website doesn't already exist.

        Returns:
            tuple: (instance, created) where created is True if a new instance was created
        """
        website = kwargs.get('website')
        if website and cls.website_exists(website):
            existing = cls.objects.get(website=website)
            return existing, False

        try:
            instance = cls.objects.create(**kwargs)
            return instance, True
        except Exception as e:
            # Handle race condition where another process inserted the same website
            if 'duplicate key value violates unique constraint' in str(e):
                existing = cls.objects.get(website=website)
                return existing, False
            raise
