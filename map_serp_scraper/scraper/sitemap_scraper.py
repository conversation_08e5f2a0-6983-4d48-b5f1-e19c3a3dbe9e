import os
import re
import asyncio
from urllib.parse import urljoin
from dotenv import load_dotenv
from xml.etree import Element<PERSON>ree
from crawl4ai import AsyncWebCrawler, BrowserConfig, ProxyConfig

load_dotenv()

PROXY_USERNAME = os.getenv("PROXY_USERNAME")
PROXY_PASSWORD = os.getenv("PROXY_PASSWORD")


async def fetch_url(crawler, url):
    try:
        response = await crawler.arun(url=url)
        if response.success and response.html:
            return response.html
        else:
            print(f"⚠️ Failed to fetch: {url}")
            return None
    except Exception as e:
        print(f"💥 Exception while fetching {url}: {e}")
        return None


def extract_sitemap_urls_from_robots(robots_text: str) -> list[str]:
    sitemap_urls = re.findall(
        r"Sitemap:\s*(https?://\S+)", robots_text, re.IGNORECASE)
    return sitemap_urls


def parse_sitemap(xml_text: str) -> list[str]:
    try:
        root = ElementTree.fromstring(xml_text)
        namespace = {"ns": "http://www.sitemaps.org/schemas/sitemap/0.9"}
        urls = []

        if root.tag.endswith("sitemapindex"):
            sitemaps = root.findall("ns:sitemap/ns:loc", namespace)
            return [s.text.strip() for s in sitemaps if s is not None]
        elif root.tag.endswith("urlset"):
            entries = root.findall("ns:url/ns:loc", namespace)
            return [e.text.strip() for e in entries if e is not None]
        else:
            return []
    except Exception as e:
        print(f"❌ Failed to parse sitemap: {e}")
        return []


async def scrape_sitemap_urls(base_url: str, use_proxy: bool = True) -> list[str]:
    domain = base_url if base_url.startswith("http") else f"https://{base_url}"
    robots_url = urljoin(domain, "/robots.txt")

    proxy_cfg = ProxyConfig(
        server="https://core-residential.evomi-proxy.com:1001",
        username=PROXY_USERNAME,
        password=PROXY_PASSWORD
    ) if use_proxy else None

    browser_cfg = BrowserConfig(
        headless=True, proxy_config=proxy_cfg, verbose=True)

    sitemap_urls = []
    discovered_urls = []

    async with AsyncWebCrawler(config=browser_cfg) as crawler:
        robots_txt = await fetch_url(crawler, robots_url)
        if not robots_txt:
            print("⚠️ No robots.txt found or empty.")
            return []

        sitemap_urls = extract_sitemap_urls_from_robots(robots_txt)
        print(f"🔍 Found {len(sitemap_urls)} sitemap(s) in robots.txt")

        queue = sitemap_urls[:]
        seen = set()

        while queue:
            current_url = queue.pop(0)
            if current_url in seen:
                continue
            seen.add(current_url)

            xml_text = await fetch_url(crawler, current_url)
            if not xml_text:
                continue

            parsed = parse_sitemap(xml_text)
            if parsed and parsed[0].endswith(".xml"):
                # likely a sitemap index
                queue.extend(parsed)
            else:
                discovered_urls.extend(parsed)

    print(f"✅ Total URLs discovered: {len(discovered_urls)}")
    return discovered_urls


def scrape_sitemap(base_url: str, use_proxy: bool = True):
    return asyncio.run(scrape_sitemap_urls(base_url, use_proxy))
