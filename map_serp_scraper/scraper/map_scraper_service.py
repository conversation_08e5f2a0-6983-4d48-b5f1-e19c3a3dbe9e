import os
from dotenv import load_dotenv
import asyncio
import json
from typing import Optional
from pydantic import BaseModel
from crawl4ai import (
    AsyncWebCrawler, BrowserConfig, CrawlerRunConfig,
    CacheMode, ProxyConfig, LLMConfig, LLMExtractionStrategy
)

load_dotenv()

PROXY_USERNAME = os.getenv("PROXY_USERNAME")
PROXY_PASSWORD = os.getenv("PROXY_PASSWORD")


class MapBusinessEntry(BaseModel):
    name: str
    rating: Optional[str]
    address: Optional[str]
    website: Optional[str]
    phone: Optional[str]
    category: Optional[str]


async def scrape_google_maps_async(query: str, use_proxy: bool = True) -> dict:
    maps_query = query.replace(" ", "+")
    search_url = f"https://www.google.com/maps/search/{maps_query}"

    print(f"\n🔍 Query: {query}")
    print(f"🔗 Google Maps URL: {search_url}")

    if use_proxy:
        print("🛡️ Using proxy for the request...")
        proxy_config = ProxyConfig(
            server="https://core-residential.evomi-proxy.com:1001",
            username=PROXY_USERNAME,
            password=PROXY_PASSWORD
        )
    else:
        print("🚫 Not using any proxy.")
        proxy_config = None

    browser_config = BrowserConfig(
        headless=True,
        proxy_config=proxy_config,
        verbose=True
    )

    extraction_strategy = LLMExtractionStrategy(
        llm_config=LLMConfig(provider="openai/gpt-4o"),
        schema=MapBusinessEntry.model_json_schema(),
        instruction="""
        Extract business information from a Google Maps search result page. 
        Include: name, rating, address, phone, website, category.
        """,
        apply_chunking=True,
        magic=True,
        chunk_token_threashold=2**12,
        verbose=True,
        input_format="raw_html",
        # input_format="cleaned_html"
        remove_overlay_elements=True
    )
    # .show_usage()

    run_config = CrawlerRunConfig(
        cache_mode=CacheMode.DISABLED,
        delay_before_return_html=6,
        keep_data_attributes=True,
        keep_attrs=["class", "id"],
        extraction_strategy=extraction_strategy,
        # ✅ Enable full page scrolling to load all results
        scan_full_page=True,
        scroll_delay=0.5,
    )

    crawler = AsyncWebCrawler(config=browser_config)
    await crawler.start()
    try:
        print("🚀 Starting crawl...")
        result = await crawler.arun(url=search_url, config=run_config)
        if not result.success:
            print(f"❌ Crawl failed: {result.error_message}")
            return {"success": False, "error": result.error_message}

        extracted = json.loads(result.extracted_content)
        print(f"✅ Crawl completed. Extracted {len(extracted)} entries.")
        return {"success": True, "data": extracted}
    except Exception as e:
        print(f"💥 Exception occurred: {e}")
        return {"success": False, "error": str(e)}
    finally:
        await crawler.close()
        print("Browser closed.")


def scrape_google_maps(query: str, use_proxy: bool = True):
    return asyncio.run(scrape_google_maps_async(query, use_proxy))
