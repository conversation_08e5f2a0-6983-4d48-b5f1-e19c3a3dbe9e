import os
import re
import json
import asyncio
from urllib.parse import urlparse
from pydantic import BaseModel
from dotenv import load_dotenv
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, KeywordRelevanceScorer, LLMConfig, LLMExtractionStrategy, ProxyConfig
from crawl4ai.deep_crawling import BFSDeepCrawlStrategy
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy

load_dotenv()

PROXY_USERNAME = os.getenv("PROXY_USERNAME")
PROXY_PASSWORD = os.getenv("PROXY_PASSWORD")

CONTACT_KEYWORDS = ["contact", "about", "support", "help", "getintouch"]
EMAIL_REGEX = re.compile(r"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+")
PHONE_REGEX = re.compile(r"\+?\d[\d\-\s()]{7,}")


class ContactInfo(BaseModel):
    url: str
    emails: list[str]
    phones: list[str]


async def scrape_site_contact_info_async(url: str, use_proxy: bool = True) -> dict:
    parsed = urlparse(url)
    base = f"{parsed.scheme}://{parsed.netloc}"

    proxy_cfg = ProxyConfig(
        server="https://core-residential.evomi-proxy.com:1001",
        username=PROXY_USERNAME, password=PROXY_PASSWORD
    ) if use_proxy else None
    # Create a scorer
    scorer = KeywordRelevanceScorer(
        keywords=[
            "contact",
            "contact-us",
            "contactus",
            "get in touch",
            "reach us",
            "reach-us",
            "support",
            "customer care",
            "customer service",
            "help desk",
            "help",
            "about",
            "about us",
            "team",
            "corporate office",
            "office address",
            "our team",
            "our office",
            "get support",
            "talk to us",
            "sales",
            "partners",
            "connect",
            "call us",
            "email us",
            "location",
            "head office",
            "enquiry",
            "enquire",
            "touch base"
        ],
        weight=0.75
    )

    browser_cfg = BrowserConfig(
        headless=True, proxy_config=proxy_cfg, verbose=True)
    deep_strategy = BFSDeepCrawlStrategy(
        max_depth=2, include_external=False, max_pages=30, url_scorer=scorer,
    )

    extraction_strategy = LLMExtractionStrategy(
        llm_config=LLMConfig(provider="openai/gpt-4o"),
        schema=ContactInfo.model_json_schema(),
        instruction="""
    Extract **only valid** business email addresses (like info@, contact@, careers@, etc.)
    and real phone numbers from the page content. Exclude version numbers, postal codes, or random digits.
    """,
        apply_chunking=True,
        chunk_token_threashold=2**12,
        magic=True,
        input_format="clean_html",
        remove_overlay_elements=True,
        verbose=True
    )
    run_cfg = CrawlerRunConfig(
        cache_mode=CacheMode.DISABLED,
        scan_full_page=True,
        scroll_delay=1.0,
        deep_crawl_strategy=deep_strategy,
        # scraping_strategy=LXMLWebScrapingStrategy(),
        extraction_strategy=extraction_strategy
    )

    async with AsyncWebCrawler(config=browser_cfg) as crawler:
        results = await crawler.arun(url=url, config=run_cfg)

    # Extract basic info from results for debugging
    results_info = {
        "total_pages": len(results),
        "pages": [{"url": page.url, "success": page.success, "status_code": getattr(page, 'status_code', None)} for page in results]
    }
    print("\n📦 Crawl Results Info:"
          f"\n{json.dumps(results_info, indent=2)}")

    contact_list = []
    for page in results:
        u = page.url.lower()
        if any(kw in u for kw in CONTACT_KEYWORDS):
            html = page.html or ""
            emails = EMAIL_REGEX.findall(html)
            phones = PHONE_REGEX.findall(html)
            if emails or phones:
                contact_list.append(ContactInfo(
                    url=page.url,
                    emails=list(set(emails)),
                    phones=list(set(phones))
                ))

    return {"success": True, "contacts": [c.dict() for c in contact_list]}


def scrape_site_contact_info(url: str, use_proxy: bool = True):
    return asyncio.run(scrape_site_contact_info_async(url, use_proxy))
