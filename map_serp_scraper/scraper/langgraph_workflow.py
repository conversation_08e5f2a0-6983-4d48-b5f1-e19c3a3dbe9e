# langgraph_workflow.py

from langchain_openai import ChatOpenAI
from pydantic import BaseModel, <PERSON>
from typing import List
from dotenv import load_dotenv
import os

load_dotenv()


class SublocationsSchema(BaseModel):
    sublocations: List[str] = Field(
        ..., description="List down sublocations name of the given city"
    )


def get_sublocations(city: str) -> List[str]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("❌ OPENAI_API_KEY not found in environment")

    print("🔐 OPENAI KEY LOADED:", api_key[:10])

    llm = ChatOpenAI(
        model="gpt-4o", api_key=api_key
    ).with_structured_output(SublocationsSchema)

    prompt = f"Give me a list of well-known sublocations (neighbourhoods) in {city}, India. Return only structured JSON."
    result: SublocationsSchema = llm.invoke(prompt)

    print("📍 Sublocations fetched:", result.sublocations)
    return result.sublocations
