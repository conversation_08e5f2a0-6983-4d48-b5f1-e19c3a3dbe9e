from scraper.langgraph_workflow import get_sublocations  # now plain function
from scraper.map_scraper_service import scrape_google_maps
from scraper.db_service import insert_scrape_entry_django
import logging
import uuid


def run_pipeline(business_type: str, city: str) -> dict:
    logging.info("🎯 Starting business data scraping pipeline...")
    logging.info(f"🚀 Target: '{business_type}' listings in '{city}'")

    summary = {"processed": [], "failed": []}

    try:
        logging.info("📍 Fetching sublocations via LLM...")
        sublocations = get_sublocations(city)

        if not sublocations:
            raise RuntimeError("LLM returned no sublocations.")

        logging.info(
            f"📍 Sublocations fetched ({len(sublocations)}): {sublocations}")
    except Exception as e:
        logging.error("❌ Failed to fetch sublocations", exc_info=True)
        return {"success": False, "error": str(e)}

    queries = [
        f"{business_type} in {subloc}, {city}" for subloc in sublocations]

    for idx, query in enumerate(queries, 1):
        trace_id = str(uuid.uuid4())[:8]
        logging.info(f"\n[{trace_id}] 🔍 [{idx}/{len(queries)}] Query: {query}")
        logging.info(
            f"[{trace_id}] 🔗 Google Maps URL: https://www.google.com/maps/search/{query.replace(' ', '+')}")

        try:
            result = scrape_google_maps(query)
            if result["success"]:
                count = len(result["data"])
                logging.info(
                    f"[{trace_id}] ✅ Crawl successful. Extracted: {count} entries")
                inserted_count = 0

                for entry in result["data"]:
                    entry["source_query"] = query
                    entry_name = entry.get('name', 'Unknown')
                    entry_website = entry.get('website', 'No website')
                    entry_phone = entry.get('phone', 'No phone')

                    logging.info(f"[{trace_id}] 🔄 Processing: {entry_name}")
                    logging.info(f"[{trace_id}]    Website: {entry_website}")
                    logging.info(f"[{trace_id}]    Phone: {entry_phone}")

                    try:
                        # Use Django ORM function for better logging and error handling
                        success, created, message = insert_scrape_entry_django(
                            entry)

                        if success and created:
                            inserted_count += 1
                            logging.info(
                                f"[{trace_id}] ✅ Successfully inserted: {entry_name}")
                        elif not created:
                            logging.info(
                                f"[{trace_id}] 🔄 Skipped (duplicate): {entry_name} - {message}")
                        else:
                            logging.warning(
                                f"[{trace_id}] ⚠️ Failed to insert: {entry_name} - {message}")

                    except Exception as insert_err:
                        logging.error(
                            f"[{trace_id}] ❌ Exception inserting {entry_name}: {str(insert_err)}", exc_info=True)

                skipped_count = count - inserted_count
                logging.info(
                    f"[{trace_id}] 📥 DB Insert Summary: {inserted_count}/{count} inserted, {skipped_count} skipped")
                summary["processed"].append({
                    "query": query,
                    "extracted": count,
                    "inserted": inserted_count,
                    "skipped": skipped_count,
                    "id": trace_id
                })
            else:
                logging.warning(
                    f"[{trace_id}] ⚠️ Scraping failed: {result.get('error', 'Unknown error')}")
                summary["failed"].append({
                    "query": query,
                    "error": result.get("error"),
                    "id": trace_id
                })
        except Exception as ex:
            logging.error(
                f"[{trace_id}] 💥 Exception while scraping: {query}", exc_info=True)
            summary["failed"].append({
                "query": query,
                "error": str(ex),
                "id": trace_id
            })

    # Calculate totals
    total_extracted = sum(p.get("extracted", 0) for p in summary["processed"])
    total_inserted = sum(p.get("inserted", 0) for p in summary["processed"])
    total_skipped = sum(p.get("skipped", 0) for p in summary["processed"])

    logging.info("\n✅ Pipeline completed.")
    logging.info(f"🟢 Total Queries Processed: {len(summary['processed'])}")
    logging.info(f"🔴 Total Queries Failed: {len(summary['failed'])}")
    logging.info(f"📊 Overall Summary:")
    logging.info(f"   📥 Total Extracted: {total_extracted}")
    logging.info(f"   ✅ Total Inserted: {total_inserted}")
    logging.info(f"   🔄 Total Skipped: {total_skipped}")

    if summary["failed"]:
        logging.warning("❗ Failed Queries Summary:")
        for failure in summary["failed"]:
            logging.warning(
                f"  - {failure['query']} | Error: {failure['error']} | ID: {failure['id']}")

    # Add totals to summary
    summary["totals"] = {
        "extracted": total_extracted,
        "inserted": total_inserted,
        "skipped": total_skipped
    }

    return summary
