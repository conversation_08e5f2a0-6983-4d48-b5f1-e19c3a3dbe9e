# Generated by Django 4.2.23 on 2025-07-09 10:54

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessScrape',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(blank=True, help_text='Business name', null=True)),
                ('rating', models.CharField(blank=True, help_text='Business rating', max_length=10, null=True)),
                ('address', models.TextField(blank=True, help_text='Business address', null=True)),
                ('website', models.URLField(blank=True, db_index=True, help_text='Business website URL (must be unique)', null=True, unique=True)),
                ('phone', models.CharField(blank=True, help_text='Business phone number', max_length=50, null=True)),
                ('category', models.CharField(blank=True, help_text='Business category', max_length=200, null=True)),
                ('source_query', models.TextField(blank=True, help_text='Original search query used', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the record was created')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the record was last updated')),
            ],
            options={
                'db_table': 'business_scrapes',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['website'], name='business_sc_website_82f968_idx'), models.Index(fields=['created_at'], name='business_sc_created_d7148d_idx'), models.Index(fields=['category'], name='business_sc_categor_d59407_idx')],
            },
        ),
    ]
