#!/usr/bin/env python3
"""
Database migration script to add unique constraint on website column.
This script can be run independently to update existing databases.
"""

import os
import psycopg2
from dotenv import load_dotenv
import logging

# Setup logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

load_dotenv()


def migrate_database():
    """
    Migrate the database to add unique constraint on website column.
    """
    try:
        # Connect to database
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            dbname=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASS"),
            port=os.getenv("DB_PORT")
        )
        cur = conn.cursor()

        logger.info("🔗 Connected to database")

        # Check if table exists
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'business_scrapes'
            );
        """)
        table_exists = cur.fetchone()[0]

        if not table_exists:
            logger.info("📋 Creating business_scrapes table...")
            # Create table with unique constraint
            cur.execute("""
                CREATE TABLE business_scrapes (
                    id SERIAL PRIMARY KEY,
                    name TEXT,
                    rating TEXT,
                    address TEXT,
                    website TEXT UNIQUE,
                    phone TEXT,
                    category TEXT,
                    source_query TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            logger.info("✅ Table created with unique constraint")
        else:
            logger.info("📋 Table exists, checking for unique constraint...")

            # Check if unique constraint exists
            cur.execute("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_name = 'business_scrapes' 
                AND constraint_type = 'UNIQUE'
                AND constraint_name LIKE '%website%';
            """)
            constraint_exists = cur.fetchone()

            if constraint_exists:
                logger.info(
                    f"✅ Unique constraint already exists: {constraint_exists[0]}")
            else:
                logger.info("🔧 Adding unique constraint on website column...")

                # Check for duplicate websites before adding constraint
                cur.execute("""
                    SELECT website, COUNT(*) 
                    FROM business_scrapes 
                    WHERE website IS NOT NULL 
                    GROUP BY website 
                    HAVING COUNT(*) > 1;
                """)
                duplicates = cur.fetchall()

                if duplicates:
                    logger.warning(
                        f"⚠️ Found {len(duplicates)} duplicate websites:")
                    for website, count in duplicates:
                        logger.warning(f"  - {website}: {count} entries")

                    # Handle duplicates by keeping the oldest entry
                    logger.info(
                        "🧹 Removing duplicate entries (keeping oldest)...")
                    cur.execute("""
                        DELETE FROM business_scrapes 
                        WHERE id NOT IN (
                            SELECT MIN(id) 
                            FROM business_scrapes 
                            WHERE website IS NOT NULL 
                            GROUP BY website
                        ) AND website IS NOT NULL;
                    """)
                    deleted_count = cur.rowcount
                    logger.info(
                        f"🗑️ Removed {deleted_count} duplicate entries")

                # Add unique constraint
                try:
                    cur.execute("""
                        ALTER TABLE business_scrapes 
                        ADD CONSTRAINT business_scrapes_website_unique UNIQUE (website);
                    """)
                    logger.info("✅ Unique constraint added successfully")
                except psycopg2.errors.DuplicateObject:
                    logger.info("✅ Unique constraint already exists")

        # Create index for performance
        logger.info("📊 Creating index on website column...")
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_business_scrapes_website 
            ON business_scrapes(website);
        """)
        logger.info("✅ Index created")

        # Add email_list column if it doesn't exist
        logger.info("📧 Checking for email_list column...")
        cur.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'business_scrapes'
            AND column_name = 'email_list';
        """)
        email_column_exists = cur.fetchone()

        if not email_column_exists:
            logger.info("➕ Adding email_list column...")
            cur.execute("""
                ALTER TABLE business_scrapes
                ADD COLUMN email_list TEXT;
            """)
            logger.info("✅ email_list column added")
        else:
            logger.info("✅ email_list column already exists")

        # Add timestamps if they don't exist
        logger.info("🕒 Checking for timestamp columns...")
        cur.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'business_scrapes'
            AND column_name IN ('created_at', 'updated_at');
        """)
        existing_columns = [row[0] for row in cur.fetchall()]

        if 'created_at' not in existing_columns:
            logger.info("➕ Adding created_at column...")
            cur.execute("""
                ALTER TABLE business_scrapes 
                ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            """)
            # Update existing rows
            cur.execute("""
                UPDATE business_scrapes 
                SET created_at = CURRENT_TIMESTAMP 
                WHERE created_at IS NULL;
            """)
            logger.info("✅ created_at column added")

        if 'updated_at' not in existing_columns:
            logger.info("➕ Adding updated_at column...")
            cur.execute("""
                ALTER TABLE business_scrapes 
                ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            """)
            # Update existing rows
            cur.execute("""
                UPDATE business_scrapes 
                SET updated_at = CURRENT_TIMESTAMP 
                WHERE updated_at IS NULL;
            """)
            logger.info("✅ updated_at column added")

        # Commit all changes
        conn.commit()

        # Show final table info
        cur.execute("""
            SELECT COUNT(*) FROM business_scrapes;
        """)
        total_count = cur.fetchone()[0]

        cur.execute("""
            SELECT COUNT(DISTINCT website) FROM business_scrapes WHERE website IS NOT NULL;
        """)
        unique_websites = cur.fetchone()[0]

        logger.info(f"📊 Migration completed successfully!")
        logger.info(f"   Total entries: {total_count}")
        logger.info(f"   Unique websites: {unique_websites}")

        cur.close()
        conn.close()

        return True

    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False


def verify_migration():
    """
    Verify that the migration was successful.
    """
    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            dbname=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASS"),
            port=os.getenv("DB_PORT")
        )
        cur = conn.cursor()

        # Check unique constraint
        cur.execute("""
            SELECT constraint_name 
            FROM information_schema.table_constraints 
            WHERE table_name = 'business_scrapes' 
            AND constraint_type = 'UNIQUE';
        """)
        constraints = cur.fetchall()

        # Check index
        cur.execute("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = 'business_scrapes' 
            AND indexname LIKE '%website%';
        """)
        indexes = cur.fetchall()

        logger.info("🔍 Verification Results:")
        logger.info(f"   Unique constraints: {[c[0] for c in constraints]}")
        logger.info(f"   Website indexes: {[i[0] for i in indexes]}")

        cur.close()
        conn.close()

        return len(constraints) > 0 and len(indexes) > 0

    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False


if __name__ == "__main__":
    logger.info("🚀 Starting database migration...")

    if migrate_database():
        logger.info("✅ Migration completed successfully")

        if verify_migration():
            logger.info("✅ Migration verified successfully")
        else:
            logger.warning("⚠️ Migration verification failed")
    else:
        logger.error("❌ Migration failed")
        exit(1)
