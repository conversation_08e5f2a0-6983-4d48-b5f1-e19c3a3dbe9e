#!/usr/bin/env python3
"""
Debug script to check database connection and current entries.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'map_serp_scraper.settings')
django.setup()

from scraper.models import BusinessScrape
from scraper.db_service import insert_scrape_entry_django
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] [%(name)s] %(message)s')
logger = logging.getLogger(__name__)


def debug_database():
    """Debug database connection and current state."""
    
    print("🔍 Database Debug Information\n")
    
    # Test 1: Check database connection
    print("1. 🔗 Testing Database Connection")
    print("-" * 40)
    try:
        total_count = BusinessScrape.objects.count()
        print(f"✅ Database connection successful")
        print(f"📊 Total entries in database: {total_count}")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return
    
    # Test 2: Show recent entries
    print(f"\n2. 📋 Recent Database Entries (Last 10)")
    print("-" * 40)
    recent_entries = BusinessScrape.objects.all().order_by('-created_at')[:10]
    
    if recent_entries:
        for i, entry in enumerate(recent_entries, 1):
            print(f"{i:2d}. {entry.name or 'No name'}")
            print(f"    Website: {entry.website or 'No website'}")
            print(f"    Phone: {entry.phone or 'No phone'}")
            print(f"    Query: {entry.source_query or 'No query'}")
            print(f"    Created: {entry.created_at}")
            print()
    else:
        print("📭 No entries found in database")
    
    # Test 3: Check for specific query patterns
    print(f"\n3. 🔍 Entries by Source Query")
    print("-" * 40)
    
    # Group by source query
    from django.db.models import Count
    query_counts = BusinessScrape.objects.values('source_query').annotate(
        count=Count('id')
    ).order_by('-count')[:10]
    
    if query_counts:
        for query_data in query_counts:
            query = query_data['source_query'] or 'No query'
            count = query_data['count']
            print(f"📝 {query}: {count} entries")
    else:
        print("📭 No query data found")
    
    # Test 4: Test insertion functionality
    print(f"\n4. 🧪 Testing Insertion Functionality")
    print("-" * 40)
    
    test_entry = {
        "name": "Debug Test Company",
        "website": f"https://debug-test-{os.getpid()}.com",
        "phone": f"******-DEBUG-{os.getpid() % 10000}",
        "emails": ["<EMAIL>"],
        "category": "Testing",
        "source_query": "debug test"
    }
    
    print(f"🔄 Attempting to insert test entry:")
    print(f"   Name: {test_entry['name']}")
    print(f"   Website: {test_entry['website']}")
    print(f"   Phone: {test_entry['phone']}")
    
    try:
        success, created, message = insert_scrape_entry_django(test_entry)
        print(f"📊 Result: Success={success}, Created={created}")
        print(f"📝 Message: {message}")
        
        if success and created:
            print("✅ Test insertion successful!")
            # Clean up test entry
            BusinessScrape.objects.filter(source_query="debug test").delete()
            print("🧹 Test entry cleaned up")
        else:
            print("⚠️ Test insertion failed or was skipped")
            
    except Exception as e:
        print(f"❌ Test insertion exception: {e}")
    
    # Test 5: Check constraints
    print(f"\n5. 🔒 Database Constraints Check")
    print("-" * 40)
    
    # Check for duplicate websites
    from django.db.models import Count
    duplicate_websites = BusinessScrape.objects.values('website').annotate(
        count=Count('id')
    ).filter(count__gt=1, website__isnull=False).exclude(website='')
    
    if duplicate_websites:
        print(f"⚠️ Found {len(duplicate_websites)} duplicate websites:")
        for dup in duplicate_websites:
            print(f"   - {dup['website']}: {dup['count']} entries")
    else:
        print("✅ No duplicate websites found")
    
    # Check for duplicate phones
    duplicate_phones = BusinessScrape.objects.values('phone').annotate(
        count=Count('id')
    ).filter(count__gt=1, phone__isnull=False).exclude(phone='')
    
    if duplicate_phones:
        print(f"⚠️ Found {len(duplicate_phones)} duplicate phone numbers:")
        for dup in duplicate_phones:
            print(f"   - {dup['phone']}: {dup['count']} entries")
    else:
        print("✅ No duplicate phone numbers found")
    
    print(f"\n✅ Database debug completed!")


if __name__ == "__main__":
    debug_database()
