#!/usr/bin/env python3
"""
Quick script to check database status and entries.
"""

import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

try:
    conn = psycopg2.connect(
        host=os.getenv('DB_HOST'),
        dbname=os.getenv('DB_NAME'),
        user=os.getenv('DB_USER'),
        password=os.getenv('DB_PASS'),
        port=os.getenv('DB_PORT')
    )
    cur = conn.cursor()
    
    # Check total entries
    cur.execute('SELECT COUNT(*) FROM business_scrapes;')
    count = cur.fetchone()[0]
    print(f'📊 Total entries in database: {count}')
    
    # Check unique websites
    cur.execute('SELECT COUNT(DISTINCT website) FROM business_scrapes WHERE website IS NOT NULL;')
    unique_websites = cur.fetchone()[0]
    print(f'🌐 Unique websites: {unique_websites}')
    
    # Show recent entries if any
    if count > 0:
        cur.execute('SELECT name, website, source_query FROM business_scrapes ORDER BY created_at DESC LIMIT 5;')
        recent = cur.fetchall()
        print('\n📋 Recent entries:')
        for name, website, query in recent:
            print(f'  - {name}: {website} (from: {query})')
    else:
        print('\n⚠️ No entries found in database')
    
    cur.close()
    conn.close()
    
except Exception as e:
    print(f'❌ Database error: {e}')
