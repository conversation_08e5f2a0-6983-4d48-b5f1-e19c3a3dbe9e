#!/usr/bin/env python3
"""
Debug script to check PostgreSQL database directly (same as pipeline uses).
"""

import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def debug_postgres_database():
    """Debug PostgreSQL database connection and current state."""
    
    print("🔍 PostgreSQL Database Debug Information\n")
    
    # Test 1: Check database connection
    print("1. 🔗 Testing PostgreSQL Database Connection")
    print("-" * 50)
    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            dbname=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASS"),
            port=os.getenv("DB_PORT")
        )
        cur = conn.cursor()
        
        # Check total entries
        cur.execute('SELECT COUNT(*) FROM business_scrapes;')
        total_count = cur.fetchone()[0]
        print(f"✅ PostgreSQL connection successful")
        print(f"📊 Total entries in PostgreSQL database: {total_count}")
        
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return
    
    # Test 2: Show recent entries
    print(f"\n2. 📋 Recent PostgreSQL Entries (Last 10)")
    print("-" * 50)
    
    if total_count > 0:
        cur.execute("""
            SELECT name, website, phone, source_query, created_at 
            FROM business_scrapes 
            ORDER BY created_at DESC 
            LIMIT 10;
        """)
        recent_entries = cur.fetchall()
        
        for i, (name, website, phone, query, created_at) in enumerate(recent_entries, 1):
            print(f"{i:2d}. {name or 'No name'}")
            print(f"    Website: {website or 'No website'}")
            print(f"    Phone: {phone or 'No phone'}")
            print(f"    Query: {query or 'No query'}")
            print(f"    Created: {created_at}")
            print()
    else:
        print("📭 No entries found in PostgreSQL database")
    
    # Test 3: Check for specific companies mentioned in logs
    print(f"\n3. 🔍 Checking for Companies from Logs")
    print("-" * 50)
    
    test_websites = [
        'https://www.lauruslabs.com/',
        'https://www.natcopharma.co.in/',
        'https://aparnapharma.com/',
        'http://www.mylan.in/',
        'http://www.calrencare.com/',
        'http://www.renspur.com/',
        'https://corvinechemicals.com/'
    ]
    
    for website in test_websites:
        cur.execute("SELECT name, phone FROM business_scrapes WHERE website = %s", (website,))
        result = cur.fetchone()
        if result:
            name, phone = result
            print(f"✅ Found: {website}")
            print(f"   Name: {name}")
            print(f"   Phone: {phone}")
        else:
            print(f"❌ Not found: {website}")
        print()
    
    # Test 4: Check constraints
    print(f"\n4. 🔒 PostgreSQL Constraints Check")
    print("-" * 50)
    
    # Check for unique constraints
    cur.execute("""
        SELECT constraint_name, column_name
        FROM information_schema.key_column_usage 
        WHERE table_name = 'business_scrapes' 
        AND constraint_name LIKE '%unique%';
    """)
    constraints = cur.fetchall()
    
    if constraints:
        print("✅ Unique constraints found:")
        for constraint_name, column_name in constraints:
            print(f"   - {constraint_name}: {column_name}")
    else:
        print("⚠️ No unique constraints found")
    
    # Test 5: Check for duplicates
    print(f"\n5. 🔍 Duplicate Check")
    print("-" * 50)
    
    # Check for duplicate websites
    cur.execute("""
        SELECT website, COUNT(*) 
        FROM business_scrapes 
        WHERE website IS NOT NULL 
        GROUP BY website 
        HAVING COUNT(*) > 1;
    """)
    duplicate_websites = cur.fetchall()
    
    if duplicate_websites:
        print(f"⚠️ Found {len(duplicate_websites)} duplicate websites:")
        for website, count in duplicate_websites:
            print(f"   - {website}: {count} entries")
    else:
        print("✅ No duplicate websites found")
    
    # Check for duplicate phones
    cur.execute("""
        SELECT phone, COUNT(*) 
        FROM business_scrapes 
        WHERE phone IS NOT NULL 
        GROUP BY phone 
        HAVING COUNT(*) > 1;
    """)
    duplicate_phones = cur.fetchall()
    
    if duplicate_phones:
        print(f"⚠️ Found {len(duplicate_phones)} duplicate phone numbers:")
        for phone, count in duplicate_phones:
            print(f"   - {phone}: {count} entries")
    else:
        print("✅ No duplicate phone numbers found")
    
    cur.close()
    conn.close()
    
    print(f"\n✅ PostgreSQL database debug completed!")


if __name__ == "__main__":
    debug_postgres_database()
