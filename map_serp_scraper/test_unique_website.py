#!/usr/bin/env python3
"""
Test script to demonstrate unique website constraint functionality.
This script tests both the raw PostgreSQL and Django ORM approaches.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'map_serp_scraper.settings')
django.setup()

from scraper.db_service import (
    insert_scrape_entry, 
    insert_scrape_entry_django, 
    bulk_insert_scrape_entries,
    website_exists
)
from scraper.models import BusinessScrape


def test_unique_constraint():
    """Test the unique website constraint functionality."""
    
    print("🧪 Testing Unique Website Constraint Functionality\n")
    
    # Test data with duplicate websites
    test_entries = [
        {
            "name": "Example Company 1",
            "rating": "4.5",
            "address": "123 Main St, City, State",
            "website": "https://example.com",
            "phone": "******-0001",
            "category": "Technology",
            "source_query": "tech companies"
        },
        {
            "name": "Example Company 2 (Duplicate Website)",
            "rating": "4.2",
            "address": "456 Oak Ave, City, State",
            "website": "https://example.com",  # Same website as above
            "phone": "******-0002",
            "category": "Software",
            "source_query": "software companies"
        },
        {
            "name": "Another Company",
            "rating": "4.8",
            "address": "789 Pine St, City, State",
            "website": "https://another-example.com",
            "phone": "******-0003",
            "category": "Consulting",
            "source_query": "consulting firms"
        },
        {
            "name": "Third Company (Another Duplicate)",
            "rating": "4.0",
            "address": "321 Elm St, City, State",
            "website": "https://example.com",  # Same website again
            "phone": "******-0004",
            "category": "Services",
            "source_query": "service companies"
        }
    ]
    
    print("📋 Test Data:")
    for i, entry in enumerate(test_entries, 1):
        print(f"  {i}. {entry['name']} - {entry['website']}")
    print()
    
    # Test 1: Django ORM approach
    print("🔧 Test 1: Django ORM Approach")
    print("-" * 40)
    
    # Clear existing test data
    BusinessScrape.objects.filter(website__in=[
        "https://example.com", 
        "https://another-example.com"
    ]).delete()
    
    for i, entry in enumerate(test_entries, 1):
        print(f"Inserting entry {i}: {entry['name']}")
        success, created, message = insert_scrape_entry_django(entry)
        print(f"  Result: Success={success}, Created={created}, Message={message}")
    
    print()
    
    # Test 2: Check website existence
    print("🔍 Test 2: Website Existence Check")
    print("-" * 40)
    
    test_websites = [
        "https://example.com",
        "https://another-example.com", 
        "https://nonexistent.com"
    ]
    
    for website in test_websites:
        exists_django = BusinessScrape.website_exists(website)
        exists_raw = website_exists(website)
        print(f"  {website}")
        print(f"    Django ORM: {exists_django}")
        print(f"    Raw SQL: {exists_raw}")
    
    print()
    
    # Test 3: Bulk insert
    print("🚀 Test 3: Bulk Insert")
    print("-" * 40)
    
    # Add some new entries with mixed duplicates
    bulk_test_entries = [
        {
            "name": "Bulk Company 1",
            "website": "https://bulk1.com",
            "category": "Bulk Test",
            "source_query": "bulk test"
        },
        {
            "name": "Bulk Company 2",
            "website": "https://example.com",  # Duplicate
            "category": "Bulk Test",
            "source_query": "bulk test"
        },
        {
            "name": "Bulk Company 3",
            "website": "https://bulk3.com",
            "category": "Bulk Test",
            "source_query": "bulk test"
        }
    ]
    
    results = bulk_insert_scrape_entries(bulk_test_entries)
    print(f"  Bulk insert results:")
    print(f"    Total entries: {results['total_entries']}")
    print(f"    Inserted: {results['inserted']}")
    print(f"    Skipped: {results['skipped']}")
    print(f"    Errors: {results['errors']}")
    if results['skipped_websites']:
        print(f"    Skipped websites: {results['skipped_websites']}")
    
    print()
    
    # Test 4: Database state verification
    print("📊 Test 4: Database State Verification")
    print("-" * 40)
    
    total_entries = BusinessScrape.objects.count()
    unique_websites = BusinessScrape.objects.values('website').distinct().count()
    
    print(f"  Total entries in database: {total_entries}")
    print(f"  Unique websites: {unique_websites}")
    
    # Show all entries
    print("\n  All entries in database:")
    for entry in BusinessScrape.objects.all().order_by('created_at'):
        print(f"    ID: {entry.id}, Name: {entry.name}, Website: {entry.website}")
    
    print()
    
    # Test 5: Raw SQL approach (for comparison)
    print("🔧 Test 5: Raw SQL Approach (for comparison)")
    print("-" * 40)
    
    raw_test_entry = {
        "name": "Raw SQL Test Company",
        "rating": "4.7",
        "address": "999 Test St",
        "website": "https://example.com",  # Duplicate
        "phone": "******-9999",
        "category": "Testing",
        "source_query": "raw sql test"
    }
    
    print(f"Inserting with raw SQL: {raw_test_entry['name']}")
    result = insert_scrape_entry(raw_test_entry)
    print(f"  Result: {result}")
    
    print("\n✅ All tests completed!")
    
    # Cleanup (optional)
    print("\n🧹 Cleanup (removing test data)")
    deleted_count = BusinessScrape.objects.filter(
        source_query__in=['tech companies', 'software companies', 'consulting firms', 'service companies', 'bulk test', 'raw sql test']
    ).delete()[0]
    print(f"  Removed {deleted_count} test entries")


if __name__ == "__main__":
    test_unique_constraint()
